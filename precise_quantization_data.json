{"timestamp": "2025-08-12 02:31:15", "summary": {"smoothquant_ops": 4, "gptq_module_compression": 7, "gptq_weight_quantization": 7}, "smoothquant_operations": [{"timestamp": 1754965874.3542666, "type": "activation_stats", "layer_name": "model.layers.0.input_layernorm", "stats": {"min_vals": [0.261474609375, 0.421630859375, -1.923828125, -2.076171875, -0.1397705078125, 0.181884765625, -0.0294647216796875, 0.08441162109375, -0.14892578125, -2.173828125, -0.413330078125, -1.09765625, -1.0185546875, -0.9189453125, -0.171875, -0.9150390625], "max_vals": [0.599609375, 1.1552734375, -0.705078125, 1.12109375, 1.1708984375, 0.301513671875, 1.1240234375, 0.54052734375, 0.5625, 1.728515625, 1.1962890625, 0.91943359375, -0.4580078125, 0.2401123046875, 1.328125, 0.89306640625], "dynamic_range": [0.338134765625, 0.7333984375, 1.21875, 3.197265625, 1.310546875, 0.11962890625, 1.1533203125, 0.4560546875, 0.71142578125, 3.90234375, 1.609375, 2.017578125, 0.560546875, 1.1591796875, 1.5, 1.80859375]}}, {"timestamp": 1754965874.355071, "type": "smoothing_transform", "layer_name": "model.layers.0.input_layernorm", "smooth_factors": {"values": [0.62548828125, 1.142578125, 1.6748046875, 3.685546875, 1.818359375, 0.2685546875, 1.63671875, 0.78125, 1.080078125, 4.34375, 2.15625, 2.57421875, 0.93115234375, 1.634765625, 2.0234375, 2.365234375], "stats": {"min": 0.2685546875, "max": 4.34375, "mean": 1.796875, "std": 1.0849609375}}, "balance_transforms": [{"module_index": 0, "module_type": "Linear", "weight_shape": [1024, 16], "std_before": 0.0199737548828125, "std_after": 0.041656494140625, "std_ratio": 2.085561497326203}, {"module_index": 1, "module_type": "Linear", "weight_shape": [1024, 16], "std_before": 0.019927978515625, "std_after": 0.041290283203125, "std_ratio": 2.0719754977029097}, {"module_index": 2, "module_type": "Linear", "weight_shape": [1024, 16], "std_before": 0.0200653076171875, "std_after": 0.04107666015625, "std_ratio": 2.047148288973384}]}, {"timestamp": 1754965874.3554711, "type": "activation_stats", "layer_name": "model.layers.0.post_attention_layernorm", "stats": {"min_vals": [-0.79541015625, -0.400146484375, -1.1279296875, -0.8701171875, 0.77783203125, 0.75732421875, -0.1126708984375, 1.302734375, -1.2890625, -2.330078125, -0.11212158203125, -1.6103515625, -0.15869140625, -0.3779296875, 0.59619140625, -1.6884765625], "max_vals": [0.427490234375, 2.0078125, 0.367919921875, 0.7177734375, 1.181640625, 1.6796875, 1.2587890625, 1.9677734375, 0.830078125, 0.69775390625, 0.8115234375, 0.2452392578125, 0.49169921875, 0.94384765625, 1.0302734375, -0.7392578125], "dynamic_range": [1.22265625, 2.408203125, 1.49609375, 1.587890625, 0.40380859375, 0.92236328125, 1.37109375, 0.6650390625, 2.119140625, 3.02734375, 0.923828125, 1.85546875, 0.650390625, 1.322265625, 0.43408203125, 0.94921875]}}, {"timestamp": 1754965874.3558693, "type": "smoothing_transform", "layer_name": "model.layers.0.post_attention_layernorm", "smooth_factors": {"values": [1.77734375, 3.275390625, 2.240234375, 2.34765625, 0.77197265625, 1.4228515625, 1.92578125, 1.1328125, 2.82421875, 3.908203125, 1.466796875, 2.541015625, 1.1513671875, 1.8701171875, 0.7880859375, 1.5498046875], "stats": {"min": 0.77197265625, "max": 3.908203125, "mean": 1.9375, "std": 0.8828125}}, "balance_transforms": [{"module_index": 0, "module_type": "Linear", "weight_shape": [32, 16], "std_before": 0.0196685791015625, "std_after": 0.03851318359375, "std_ratio": 1.9581070597362296}, {"module_index": 1, "module_type": "Linear", "weight_shape": [32, 16], "std_before": 0.0197296142578125, "std_after": 0.041778564453125, "std_ratio": 2.1175560711523587}]}], "gptq_module_compression": [{"compression_id": 0, "timestamp": 1754965874.4376218, "module_name": "model.layers.0.self_attn.q_proj", "num_samples": 8, "quant_strategy": "channel", "quant_observer": "minmax", "module_info": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "device": "cuda:0"}}, {"compression_id": 1, "timestamp": 1754965874.525946, "module_name": "model.layers.0.self_attn.k_proj", "num_samples": 8, "quant_strategy": "channel", "quant_observer": "minmax", "module_info": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "device": "cuda:0"}}, {"compression_id": 2, "timestamp": 1754965874.534895, "module_name": "model.layers.0.self_attn.v_proj", "num_samples": 8, "quant_strategy": "channel", "quant_observer": "minmax", "module_info": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "device": "cuda:0"}}, {"compression_id": 3, "timestamp": 1754965874.543475, "module_name": "model.layers.0.self_attn.o_proj", "num_samples": 8, "quant_strategy": "channel", "quant_observer": "minmax", "module_info": {"weight_shape": [16, 1024], "weight_dtype": "torch.float16", "device": "cuda:0"}}, {"compression_id": 4, "timestamp": 1754965875.0075846, "module_name": "model.layers.0.mlp.gate_proj", "num_samples": 8, "quant_strategy": "channel", "quant_observer": "minmax", "module_info": {"weight_shape": [32, 16], "weight_dtype": "torch.float16", "device": "cuda:0"}}, {"compression_id": 5, "timestamp": 1754965875.0138507, "module_name": "model.layers.0.mlp.up_proj", "num_samples": 8, "quant_strategy": "channel", "quant_observer": "minmax", "module_info": {"weight_shape": [32, 16], "weight_dtype": "torch.float16", "device": "cuda:0"}}, {"compression_id": 6, "timestamp": 1754965875.0198145, "module_name": "model.layers.0.mlp.down_proj", "num_samples": 8, "quant_strategy": "channel", "quant_observer": "minmax", "module_info": {"weight_shape": [16, 32], "weight_dtype": "torch.float16", "device": "cuda:0"}}], "gptq_weight_quantization": [{"compression_id": 0, "timestamp": 1754965874.5257099, "weight_shape": [1024, 16], "hessian_info": {"condition_number": 464512640.0, "dampening_factor": "tensor(0.6315, device='cuda:0')", "inversion_success": true, "dead_neurons": 0}, "observer_scales": {"strategy": "channel", "shape": [1024, 1], "min": 0.00022918103786651045, "max": 0.0022192862816154957, "mean": 0.0008165809558704495, "std": 0.00030690288986079395, "values": null}, "block_analysis": [{"block_index": 0, "column_range": [0, 16], "block_size": 16, "block_loss": 1.4928252767276717e-07, "column_details": [{"column_index": 0, "quantization_type": "per_channel", "scale_used": 0.0008165809558704495, "quantization_error": 0.00020459094957914203, "weight_stats": {"mean": 0.00047584204003214836, "std": 0.012055996805429459}}, {"column_index": 1, "quantization_type": "per_channel", "scale_used": 0.0008165809558704495, "quantization_error": 0.00020576731185428798, "weight_stats": {"mean": 0.00017323176143690944, "std": 0.023029040545225143}}, {"column_index": 2, "quantization_type": "per_channel", "scale_used": 0.0008165809558704495, "quantization_error": 0.00020343612413853407, "weight_stats": {"mean": -0.00048128614434972405, "std": 0.033614423125982285}}]}], "final_results": {"total_loss": 0.0024458449333906174, "final_scale_stats": {"shape": [1024, 1], "min": 0.00022918103786651045, "max": 0.0022192862816154957, "mean": 0.0008165809558704495}}}, {"compression_id": 1, "timestamp": 1754965874.5347147, "weight_shape": [1024, 16], "hessian_info": {"condition_number": 464512640.0, "dampening_factor": "tensor(0.6315, device='cuda:0')", "inversion_success": true, "dead_neurons": 0}, "observer_scales": {"strategy": "channel", "shape": [1024, 1], "min": 0.000257305073319003, "max": 0.0025543812662363052, "mean": 0.0008088488830253482, "std": 0.0003203052037861198, "values": null}, "block_analysis": [{"block_index": 0, "column_range": [0, 16], "block_size": 16, "block_loss": 1.5324550872719556e-07, "column_details": [{"column_index": 0, "quantization_type": "per_channel", "scale_used": 0.0008088488830253482, "quantization_error": 0.00020255609706509858, "weight_stats": {"mean": -0.000138135626912117, "std": 0.012599997222423553}}, {"column_index": 1, "quantization_type": "per_channel", "scale_used": 0.0008088488830253482, "quantization_error": 0.00020739747560583055, "weight_stats": {"mean": -0.0007260916754603386, "std": 0.02306130900979042}}, {"column_index": 2, "quantization_type": "per_channel", "scale_used": 0.0008088488830253482, "quantization_error": 0.00020295455760788172, "weight_stats": {"mean": -0.0005783084779977798, "std": 0.033199552446603775}}]}], "final_results": {"total_loss": 0.002510774414986372, "final_scale_stats": {"shape": [1024, 1], "min": 0.000257305073319003, "max": 0.0025543812662363052, "mean": 0.0008088488830253482}}}, {"compression_id": 2, "timestamp": 1754965874.5433118, "weight_shape": [1024, 16], "hessian_info": {"condition_number": 464512640.0, "dampening_factor": "tensor(0.6315, device='cuda:0')", "inversion_success": true, "dead_neurons": 0}, "observer_scales": {"strategy": "channel", "shape": [1024, 1], "min": 0.0002169740037061274, "max": 0.0020526961889117956, "mean": 0.0008007469587028027, "std": 0.0002926407614722848, "values": null}, "block_analysis": [{"block_index": 0, "column_range": [0, 16], "block_size": 16, "block_loss": 1.4668975722997857e-07, "column_details": [{"column_index": 0, "quantization_type": "per_channel", "scale_used": 0.0008007469587028027, "quantization_error": 0.00019795361731667072, "weight_stats": {"mean": -0.000266176532022655, "std": 0.012514709495007992}}, {"column_index": 1, "quantization_type": "per_channel", "scale_used": 0.0008007469587028027, "quantization_error": 0.0002027704322244972, "weight_stats": {"mean": -0.0008323469664901495, "std": 0.022786781191825867}}, {"column_index": 2, "quantization_type": "per_channel", "scale_used": 0.0008007469587028027, "quantization_error": 0.00020286273502279073, "weight_stats": {"mean": -0.0011252182302996516, "std": 0.033572208136320114}}]}], "final_results": {"total_loss": 0.002403364982455969, "final_scale_stats": {"shape": [1024, 1], "min": 0.0002169740037061274, "max": 0.0020526961889117956, "mean": 0.0008007469587028027}}}, {"compression_id": 3, "timestamp": 1754965875.0074265, "weight_shape": [16, 1024], "hessian_info": {"condition_number": 1838051885056.0, "dampening_factor": "tensor(0.0053, device='cuda:0')", "inversion_success": true, "dead_neurons": 0}, "observer_scales": {"strategy": "channel", "shape": [16, 1], "min": 0.00047511683078482747, "max": 0.0006314147030934691, "mean": 0.0005461300606839359, "std": 4.653562427847646e-05, "values": [0.0006223192904144526, 0.0005280139739625156, 0.0005447686999104917, 0.0005892884801141918, 0.00047511683078482747, 0.0005227482179179788, 0.000518918561283499, 0.0005572151276282966, 0.0005643957410939038, 0.0005069508333690464]}, "block_analysis": [{"block_index": 0, "column_range": [0, 128], "block_size": 128, "block_loss": 1.3403222975938434e-10, "column_details": [{"column_index": 0, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00013740059512201697, "weight_stats": {"mean": 0.004690214991569519, "std": 0.022654926404356956}}, {"column_index": 1, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00014668889343738556, "weight_stats": {"mean": -0.0010183453559875488, "std": 0.021115511655807495}}, {"column_index": 2, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00013559551734942943, "weight_stats": {"mean": 0.002856731414794922, "std": 0.01848421059548855}}]}, {"block_index": 1, "column_range": [128, 256], "block_size": 128, "block_loss": 1.301873331360781e-10, "column_details": [{"column_index": 128, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.0001358543522655964, "weight_stats": {"mean": -0.006786637008190155, "std": 0.02616710402071476}}, {"column_index": 129, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00014746072702109814, "weight_stats": {"mean": 0.008093483746051788, "std": 0.02384648285806179}}, {"column_index": 130, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.000153452274389565, "weight_stats": {"mean": 0.005796865560114384, "std": 0.018626980483531952}}]}, {"block_index": 2, "column_range": [256, 384], "block_size": 128, "block_loss": 1.3282620836552184e-10, "column_details": [{"column_index": 256, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.0001226803578902036, "weight_stats": {"mean": 0.0005496633239090443, "std": 0.01865982823073864}}, {"column_index": 257, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00013941427459940314, "weight_stats": {"mean": -0.005211884621530771, "std": 0.02101905830204487}}, {"column_index": 258, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00018168127280659974, "weight_stats": {"mean": 0.00019369181245565414, "std": 0.018627969548106194}}]}, {"block_index": 3, "column_range": [384, 512], "block_size": 128, "block_loss": 1.340103306102236e-10, "column_details": [{"column_index": 384, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00015612642164342105, "weight_stats": {"mean": -0.007454719860106707, "std": 0.017270740121603012}}, {"column_index": 385, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00013664971629623324, "weight_stats": {"mean": 0.006433588452637196, "std": 0.017346607521176338}}, {"column_index": 386, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.0001276266557397321, "weight_stats": {"mean": 0.002035879995673895, "std": 0.019627992063760757}}]}, {"block_index": 4, "column_range": [512, 640], "block_size": 128, "block_loss": 1.3794754227802741e-10, "column_details": [{"column_index": 512, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00012811404303647578, "weight_stats": {"mean": 0.005480870138853788, "std": 0.02113969624042511}}, {"column_index": 513, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00012638757470995188, "weight_stats": {"mean": -0.0008379719220101833, "std": 0.01617458648979664}}, {"column_index": 514, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.000154172521433793, "weight_stats": {"mean": -0.0014614509418606758, "std": 0.015931133180856705}}]}, {"block_index": 5, "column_range": [640, 768], "block_size": 128, "block_loss": 1.3121588537945428e-10, "column_details": [{"column_index": 640, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00014564950834028423, "weight_stats": {"mean": 0.0019194186897948384, "std": 0.019676461815834045}}, {"column_index": 641, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.0001301929005421698, "weight_stats": {"mean": -0.012354926206171513, "std": 0.019444867968559265}}, {"column_index": 642, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00013891658454667777, "weight_stats": {"mean": 0.0027746506966650486, "std": 0.01973850280046463}}]}, {"block_index": 6, "column_range": [768, 896], "block_size": 128, "block_loss": 1.318824216500758e-10, "column_details": [{"column_index": 768, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00014258091687224805, "weight_stats": {"mean": -0.002579805674031377, "std": 0.021006500348448753}}, {"column_index": 769, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00014749233378097415, "weight_stats": {"mean": 0.002087065251544118, "std": 0.02262440137565136}}, {"column_index": 770, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.0001584595738677308, "weight_stats": {"mean": 0.005856029223650694, "std": 0.018843641504645348}}]}, {"block_index": 7, "column_range": [896, 1024], "block_size": 128, "block_loss": 2.578478819348362e-10, "column_details": [{"column_index": 896, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00014513092173729092, "weight_stats": {"mean": -0.004765098448842764, "std": 0.015600018203258514}}, {"column_index": 897, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 9.137977031059563e-05, "weight_stats": {"mean": 0.0034851033706218004, "std": 0.020046230405569077}}, {"column_index": 898, "quantization_type": "per_channel", "scale_used": 0.0005461300606839359, "quantization_error": 0.00011107134923804551, "weight_stats": {"mean": 0.008278381079435349, "std": 0.02258165366947651}}]}], "final_results": {"total_loss": 2.4370174287469126e-06, "final_scale_stats": {"shape": [16, 1], "min": 0.00047511683078482747, "max": 0.0006314147030934691, "mean": 0.0005461300606839359}}}, {"compression_id": 4, "timestamp": 1754965875.0137262, "weight_shape": [32, 16], "hessian_info": {"condition_number": 249683024.0, "dampening_factor": "tensor(1.2602, device='cuda:0')", "inversion_success": true, "dead_neurons": 0}, "observer_scales": {"strategy": "channel", "shape": [32, 1], "min": 0.0003154680016450584, "max": 0.0010426241206005216, "mean": 0.0006921133026480675, "std": 0.00017384237435180694, "values": [0.0006347656599245965, 0.0003765031578950584, 0.0010426241206005216, 0.000546683557331562, 0.0007252413197420537, 0.0006835937965661287, 0.0006969975656829774, 0.000912894494831562, 0.0006467333296313882, 0.0006165747763589025]}, "block_analysis": [{"block_index": 0, "column_range": [0, 16], "block_size": 16, "block_loss": 8.088742902145896e-07, "column_details": [{"column_index": 0, "quantization_type": "per_channel", "scale_used": 0.0006921133026480675, "quantization_error": 0.00019819535373244435, "weight_stats": {"mean": -0.003801703453063965, "std": 0.035359274595975876}}, {"column_index": 1, "quantization_type": "per_channel", "scale_used": 0.0006921133026480675, "quantization_error": 0.00018219905905425549, "weight_stats": {"mean": -0.0016668736934661865, "std": 0.055333394557237625}}, {"column_index": 2, "quantization_type": "per_channel", "scale_used": 0.0006921133026480675, "quantization_error": 0.00017362696235068142, "weight_stats": {"mean": 0.01019527018070221, "std": 0.03743390738964081}}]}], "final_results": {"total_loss": 0.00041414363658986986, "final_scale_stats": {"shape": [32, 1], "min": 0.0003154680016450584, "max": 0.0010426241206005216, "mean": 0.0006921133026480675}}}, {"compression_id": 5, "timestamp": 1754965875.019701, "weight_shape": [32, 16], "hessian_info": {"condition_number": 249683024.0, "dampening_factor": "tensor(1.2602, device='cuda:0')", "inversion_success": true, "dead_neurons": 0}, "observer_scales": {"strategy": "channel", "shape": [32, 1], "min": 0.0003477807331364602, "max": 0.0014083563582971692, "mean": 0.0008251490071415901, "std": 0.00029753867420367897, "values": [0.0003477807331364602, 0.0012896370608359575, 0.0012657017214223742, 0.0005217907601036131, 0.0008214614354074001, 0.0008003983530215919, 0.0011144302552565932, 0.0009674671455286443, 0.001136450795456767, 0.0014083563582971692]}, "block_analysis": [{"block_index": 0, "column_range": [0, 16], "block_size": 16, "block_loss": 9.888569820759585e-07, "column_details": [{"column_index": 0, "quantization_type": "per_channel", "scale_used": 0.0008251490071415901, "quantization_error": 0.00019130459986627102, "weight_stats": {"mean": -0.0022793784737586975, "std": 0.03260637819766998}}, {"column_index": 1, "quantization_type": "per_channel", "scale_used": 0.0008251490071415901, "quantization_error": 0.00022497544705402106, "weight_stats": {"mean": -0.0005652904510498047, "std": 0.06116390973329544}}, {"column_index": 2, "quantization_type": "per_channel", "scale_used": 0.0008251490071415901, "quantization_error": 0.00023859844077378511, "weight_stats": {"mean": 0.002392098307609558, "std": 0.04548520967364311}}]}], "final_results": {"total_loss": 0.0005062947748228908, "final_scale_stats": {"shape": [32, 1], "min": 0.0003477807331364602, "max": 0.0014083563582971692, "mean": 0.0008251490071415901}}}, {"compression_id": 6, "timestamp": 1754965875.0293882, "weight_shape": [16, 32], "hessian_info": {"condition_number": 5420798464.0, "dampening_factor": "tensor(3.6166e-05, device='cuda:0')", "inversion_success": true, "dead_neurons": 0}, "observer_scales": {"strategy": "channel", "shape": [16, 1], "min": 0.00022954007727093995, "max": 0.00046123433276079595, "mean": 0.00036561256274580956, "std": 7.06445935065858e-05, "values": [0.00022954007727093995, 0.00031953700818121433, 0.00045142084127292037, 0.00029560166876763105, 0.0003827263426501304, 0.00044328279909677804, 0.00034969556145370007, 0.0003614238812588155, 0.00045142084127292037, 0.00026352828717790544]}, "block_analysis": [{"block_index": 0, "column_range": [0, 32], "block_size": 32, "block_loss": 1.1009486508214983e-12, "column_details": [{"column_index": 0, "quantization_type": "per_channel", "scale_used": 0.00036561256274580956, "quantization_error": 0.00010794901754707098, "weight_stats": {"mean": 0.003769993782043457, "std": 0.019674325361847878}}, {"column_index": 1, "quantization_type": "per_channel", "scale_used": 0.00036561256274580956, "quantization_error": 9.081617463380098e-05, "weight_stats": {"mean": -0.003983974456787109, "std": 0.022880472242832184}}, {"column_index": 2, "quantization_type": "per_channel", "scale_used": 0.00036561256274580956, "quantization_error": 8.729140245122835e-05, "weight_stats": {"mean": 0.000924915075302124, "std": 0.015875719487667084}}]}], "final_results": {"total_loss": 5.636857092206071e-10, "final_scale_stats": {"shape": [16, 1], "min": 0.00022954007727093995, "max": 0.00046123433276079595, "mean": 0.00036561256274580956}}}], "timeline": [{"timestamp": 1754965874.4376247, "phase": "GPTQ", "operation": "module_compression_start", "details": {"compression_id": 0, "module_name": "model.layers.0.self_attn.q_proj"}}, {"timestamp": 1754965874.52595, "phase": "GPTQ", "operation": "module_compression_start", "details": {"compression_id": 1, "module_name": "model.layers.0.self_attn.k_proj"}}, {"timestamp": 1754965874.5348978, "phase": "GPTQ", "operation": "module_compression_start", "details": {"compression_id": 2, "module_name": "model.layers.0.self_attn.v_proj"}}, {"timestamp": 1754965874.5434775, "phase": "GPTQ", "operation": "module_compression_start", "details": {"compression_id": 3, "module_name": "model.layers.0.self_attn.o_proj"}}, {"timestamp": 1754965875.0075874, "phase": "GPTQ", "operation": "module_compression_start", "details": {"compression_id": 4, "module_name": "model.layers.0.mlp.gate_proj"}}, {"timestamp": 1754965875.0138521, "phase": "GPTQ", "operation": "module_compression_start", "details": {"compression_id": 5, "module_name": "model.layers.0.mlp.up_proj"}}, {"timestamp": 1754965875.0198157, "phase": "GPTQ", "operation": "module_compression_start", "details": {"compression_id": 6, "module_name": "model.layers.0.mlp.down_proj"}}]}