# SmoothQuant量化前后模型变化详细对比表

## 📋 总体变化概览

| 指标 | 量化前 | 量化后 | 变化说明 |
|------|--------|--------|----------|
| **总参数数量** | 1,091,152 | 1,097,488 | +6,336个量化参数 |
| **参数类型分布** | 100% FP16 | 99.7% FP16 + 0.3% INT8 | 新增INT8 zero_point参数 |
| **模型大小** | ~2.1MB | ~2.2MB | +约100KB量化参数 |
| **量化状态** | 未量化 | 压缩量化 | compressed-tensors格式 |

## 🔍 网络层级详细变化对比

### 1. 注意力机制层变化

#### 1.1 input_layernorm层
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [16] | [16] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | 1.000000 | 0.623535 | -37.6% |
| **权重标准差** | 0.000000 | 0.449707 | +∞ (从0变为非0) |
| **权重范围** | [1.0, 1.0] | [0.572754, 4.343750] | 动态范围大幅增加 |
| **bias** | 存在 | 同样被平滑因子调整 | 按相同比例缩放 |
| **功能变化** | 标准LayerNorm | 平滑后的LayerNorm | 输出被平滑因子调整 |

#### 1.2 q_proj层 (查询投影)
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [1024, 16] | [1024, 16] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | 0.000198 | 0.000145 | -26.8% |
| **权重标准差** | 0.019974 | 0.048401 | +142.3% |
| **权重范围** | [-0.075, 0.080] | 扩大约2.4倍 | 动态范围增加 |
| **新增参数** | 无 | weight_scale [1024, 1] FP16 | +1024个scale参数 |
| **新增参数** | 无 | weight_zero_point [1024, 1] INT8 | +1024个zero_point参数 |
| **量化损失** | N/A | 0.00386541 | 中等精度损失 |
| **压缩大小** | N/A | 0.03584 MB | 量化后大小 |

#### 1.3 k_proj层 (键投影)
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [1024, 16] | [1024, 16] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | 0.000028 | 0.000108 | +285.7% |
| **权重标准差** | 0.019928 | 0.048035 | +141.0% |
| **新增参数** | 无 | weight_scale [1024, 1] FP16 | +1024个scale参数 |
| **新增参数** | 无 | weight_zero_point [1024, 1] INT8 | +1024个zero_point参数 |
| **量化损失** | N/A | 0.00397363 | 中等精度损失 |
| **压缩大小** | N/A | 0.03584 MB | 量化后大小 |

#### 1.4 v_proj层 (值投影)
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [1024, 16] | [1024, 16] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | -0.000178 | -0.000551 | +209.6% |
| **权重标准差** | 0.020065 | 0.048035 | +139.4% |
| **新增参数** | 无 | weight_scale [1024, 1] FP16 | +1024个scale参数 |
| **新增参数** | 无 | weight_zero_point [1024, 1] INT8 | +1024个zero_point参数 |
| **量化损失** | N/A | 0.00370757 | 中等精度损失 |
| **压缩大小** | N/A | 0.03584 MB | 量化后大小 |

#### 1.5 o_proj层 (输出投影) ⭐ 特殊层
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [16, 1024] | [16, 1024] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | -0.000078 | -0.000078 | 0% (未受SmoothQuant影响) |
| **权重标准差** | 0.020020 | 0.020019 | -0.005% (几乎无变化) |
| **新增参数** | 无 | weight_scale [16, 1] FP16 | +16个scale参数 |
| **新增参数** | 无 | weight_zero_point [16, 1] INT8 | +16个zero_point参数 |
| **量化损失** | N/A | 0.00000463 | 极低精度损失 ⭐ |
| **压缩大小** | N/A | 0.032816 MB | 量化后大小 |
| **特殊性** | 普通Linear层 | 未受SmoothQuant平滑影响 | 保持原始权重分布 |

### 2. MLP层变化

#### 2.1 post_attention_layernorm层
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [16] | [16] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | 1.000000 | 0.492188 | -50.8% |
| **权重标准差** | 0.000000 | 0.178101 | +∞ (从0变为非0) |
| **权重范围** | [1.0, 1.0] | [1.052734, 3.949219] | 动态范围大幅增加 |
| **功能变化** | 标准LayerNorm | 平滑后的LayerNorm | 输出被平滑因子调整 |

#### 2.2 gate_proj层 (门控投影)
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [32, 16] | [32, 16] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | -0.000425 | -0.000281 | -33.9% |
| **权重标准差** | 0.019669 | 0.044800 | +127.8% |
| **新增参数** | 无 | weight_scale [32, 1] FP16 | +32个scale参数 |
| **新增参数** | 无 | weight_zero_point [32, 1] INT8 | +32个zero_point参数 |
| **量化损失** | N/A | 0.00065096 | 低精度损失 |
| **压缩大小** | N/A | 0.00112 MB | 量化后大小 |

#### 2.3 up_proj层 (上投影)
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [32, 16] | [32, 16] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | -0.000601 | -0.002069 | +244.4% |
| **权重标准差** | 0.019730 | 0.047760 | +142.1% |
| **新增参数** | 无 | weight_scale [32, 1] FP16 | +32个scale参数 |
| **新增参数** | 无 | weight_zero_point [32, 1] INT8 | +32个zero_point参数 |
| **量化损失** | N/A | 0.00094338 | 低精度损失 |
| **压缩大小** | N/A | 0.00112 MB | 量化后大小 |

#### 2.4 down_proj层 (下投影) ⭐ 特殊层
| 属性 | 量化前 | 量化后 | 变化幅度 |
|------|--------|--------|----------|
| **权重形状** | [16, 32] | [16, 32] | 无变化 |
| **权重数据类型** | torch.float16 | torch.float16 | 无变化 |
| **权重均值** | -0.000932 | -0.000930 | -0.2% (几乎无变化) |
| **权重标准差** | 0.020050 | 0.020040 | -0.05% (几乎无变化) |
| **新增参数** | 无 | weight_scale [16, 1] FP16 | +16个scale参数 |
| **新增参数** | 无 | weight_zero_point [16, 1] INT8 | +16个zero_point参数 |
| **量化损失** | N/A | 0.00000000 | 完美量化 ⭐ |
| **压缩大小** | N/A | 0.001072 MB | 量化后大小 |
| **特殊性** | 普通Linear层 | 未受SmoothQuant平滑影响 | 保持原始权重分布 |

## 📊 激活值和数据流变化

### 1. 激活值精度变化
| 层类型 | 量化前激活 | 量化后激活 | 变化说明 |
|--------|------------|------------|----------|
| **LayerNorm输出** | FP16 | FP16 (平滑后) | 数值被平滑因子调整，但仍为FP16 |
| **Linear层输入** | FP16 | FP16 | 在推理时可能被动态量化为INT8 |
| **Linear层输出** | FP16 | FP16 | 保持FP16精度 |
| **最终输出** | FP16 | FP16 | 模型输出精度不变 |

### 2. 数据流变化
| 阶段 | 量化前 | 量化后 | 关键变化 |
|------|--------|--------|----------|
| **校准阶段** | 标准前向传播 | 收集激活统计 + 应用平滑变换 | 新增统计收集和权重调整 |
| **推理阶段** | FP16计算 | 可选的动态激活量化 + FP16计算 | 可启用W8A8推理 |
| **内存占用** | 基准 | +6,336个量化参数 | 增加约100KB |

## 🔧 Buffer和状态变化

### 1. 新增Buffer参数
| 层名称 | 新增Buffer | 数据类型 | 形状 | 用途 |
|--------|------------|----------|------|------|
| **q_proj** | weight_scale | FP16 | [1024, 1] | Channel-wise缩放因子 |
| **q_proj** | weight_zero_point | INT8 | [1024, 1] | Channel-wise零点 |
| **k_proj** | weight_scale | FP16 | [1024, 1] | Channel-wise缩放因子 |
| **k_proj** | weight_zero_point | INT8 | [1024, 1] | Channel-wise零点 |
| **v_proj** | weight_scale | FP16 | [1024, 1] | Channel-wise缩放因子 |
| **v_proj** | weight_zero_point | INT8 | [1024, 1] | Channel-wise零点 |
| **o_proj** | weight_scale | FP16 | [16, 1] | Channel-wise缩放因子 |
| **o_proj** | weight_zero_point | INT8 | [16, 1] | Channel-wise零点 |
| **gate_proj** | weight_scale | FP16 | [32, 1] | Channel-wise缩放因子 |
| **gate_proj** | weight_zero_point | INT8 | [32, 1] | Channel-wise零点 |
| **up_proj** | weight_scale | FP16 | [32, 1] | Channel-wise缩放因子 |
| **up_proj** | weight_zero_point | INT8 | [32, 1] | Channel-wise零点 |
| **down_proj** | weight_scale | FP16 | [16, 1] | Channel-wise缩放因子 |
| **down_proj** | weight_zero_point | INT8 | [16, 1] | Channel-wise零点 |

### 2. 量化配置状态
| 属性 | 量化前 | 量化后 | 说明 |
|------|--------|--------|------|
| **quantization_status** | 无 | "compressed" | 标记为已压缩 |
| **quant_method** | 无 | "compressed-tensors" | 使用compressed-tensors格式 |
| **format** | 无 | "int-quantized" | INT量化格式 |

## 📈 性能和精度总结

### 1. 量化精度排序 (从高到低)
| 排名 | 层名称 | 量化损失 | 精度等级 | SmoothQuant影响 |
|------|--------|----------|----------|-----------------|
| 1 | down_proj | 0.00000000 | 完美 ⭐ | 无影响 |
| 2 | o_proj | 0.00000463 | 极高 ⭐ | 无影响 |
| 3 | gate_proj | 0.00065096 | 高 | 受影响 |
| 4 | up_proj | 0.00094338 | 高 | 受影响 |
| 5 | v_proj | 0.00370757 | 中等 | 受影响 |
| 6 | q_proj | 0.00386541 | 中等 | 受影响 |
| 7 | k_proj | 0.00397363 | 中等 | 受影响 |

### 2. 关键发现
- **最佳量化层**: o_proj和down_proj (未受SmoothQuant影响)
- **权重变化最大**: 受SmoothQuant影响的层权重std增加约140%
- **LayerNorm调整**: 权重均值减少38%-51%，承担平滑功能
- **总体效果**: 在增加少量参数的情况下实现高精度量化

### 3. SmoothQuant的核心作用
- **激活平滑**: 通过LayerNorm权重调整减少激活异常值
- **权重补偿**: 通过Linear权重调整保持数学等价性
- **量化优化**: 为后续GPTQ量化创造更好的条件
- **精度保持**: 整体模型精度基本保持，部分层甚至完美量化
