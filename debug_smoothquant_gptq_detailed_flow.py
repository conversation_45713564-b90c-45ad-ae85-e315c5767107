#!/usr/bin/env python3
"""
Debug脚本：深入分析SmoothQuant + GPTQ的完整量化流程
重点追踪scale处理和per-channel/per-block冲突解决方案
"""

import torch
import time
import copy
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

class DetailedQuantizationTracker:
    """详细的量化流程追踪器"""
    
    def __init__(self):
        self.smoothquant_operations = []
        self.gptq_operations = []
        self.scale_calculations = []
        self.block_operations = []
        self.hessian_operations = []
        self.weight_transformations = []
        
    def track_smoothquant_operation(self, operation_type, layer_name, data):
        """追踪SmoothQuant操作"""
        self.smoothquant_operations.append({
            'timestamp': time.time(),
            'operation_type': operation_type,
            'layer_name': layer_name,
            'data': data
        })
    
    def track_gptq_operation(self, operation_type, layer_name, data):
        """追踪GPTQ操作"""
        self.gptq_operations.append({
            'timestamp': time.time(),
            'operation_type': operation_type,
            'layer_name': layer_name,
            'data': data
        })
    
    def track_scale_calculation(self, calculation_type, layer_name, scale_data):
        """追踪scale计算"""
        self.scale_calculations.append({
            'timestamp': time.time(),
            'calculation_type': calculation_type,
            'layer_name': layer_name,
            'scale_data': scale_data
        })
    
    def track_block_operation(self, layer_name, block_info):
        """追踪块操作"""
        self.block_operations.append({
            'timestamp': time.time(),
            'layer_name': layer_name,
            'block_info': block_info
        })

def patch_smoothquant_for_detailed_tracking():
    """给SmoothQuant打详细追踪补丁"""
    from llmcompressor.modifiers.smoothquant.base import SmoothQuantModifier
    
    global tracker
    tracker = DetailedQuantizationTracker()
    
    # 1. 追踪激活统计收集
    original_setup_hooks = SmoothQuantModifier._setup_scale_hooks
    
    def tracked_setup_scale_hooks(self):
        print(f"\n🔧 SmoothQuant._setup_scale_hooks 执行")
        print(f"   映射数量: {len(self.resolved_mappings_)}")
        
        def create_tracked_hook_fn(layer_name):
            def hook_fn(module, inp, out):
                # 处理输出
                if isinstance(out, tuple):
                    out = out[0]
                
                # 重塑为2D
                hidden_dim = out.shape[-1]
                out_2d = out.view(-1, hidden_dim)
                
                # 计算统计
                latest_mins = torch.min(out_2d, dim=0)[0]
                latest_maxes = torch.max(out_2d, dim=0)[0]
                
                # 追踪操作
                tracker.track_smoothquant_operation(
                    'activation_stats_collection',
                    layer_name,
                    {
                        'input_shape': list(inp[0].shape) if isinstance(inp, tuple) else list(inp.shape),
                        'output_shape': list(out.shape),
                        'output_2d_shape': list(out_2d.shape),
                        'mins_range': [latest_mins.min().item(), latest_mins.max().item()],
                        'maxes_range': [latest_maxes.min().item(), latest_maxes.max().item()],
                        'dynamic_range': [(latest_maxes - latest_mins).min().item(), 
                                        (latest_maxes - latest_mins).max().item()]
                    }
                )
                
                print(f"🔍 激活统计收集: {layer_name}")
                print(f"   输出形状: {out.shape} -> 2D: {out_2d.shape}")
                print(f"   动态范围: [{(latest_maxes - latest_mins).min():.6f}, {(latest_maxes - latest_mins).max():.6f}]")
                
                # 更新统计
                if layer_name in self.scales_:
                    self.scales_[layer_name].min_channel_vals = torch.minimum(
                        self.scales_[layer_name].min_channel_vals, latest_mins
                    )
                    self.scales_[layer_name].max_channel_vals = torch.maximum(
                        self.scales_[layer_name].max_channel_vals, latest_maxes
                    )
                else:
                    from llmcompressor.modifiers.smoothquant.base import SmoothQuantScale
                    self.scales_[layer_name] = SmoothQuantScale(
                        min_channel_vals=latest_mins,
                        max_channel_vals=latest_maxes
                    )
            
            return hook_fn
        
        # 为每个映射注册hook
        for mapping in self.resolved_mappings_:
            name = mapping.smooth_name
            layer = mapping.smooth_layer
            self.register_hook(layer, create_tracked_hook_fn(name), "forward")
            print(f"   注册hook: {name}")
        
        return original_setup_hooks(self)
    
    # 2. 追踪平滑变换应用
    original_apply_smoothing = SmoothQuantModifier._apply_smoothing
    
    def tracked_apply_smoothing(self, model):
        print(f"\n🔧 SmoothQuant._apply_smoothing 执行")
        
        for mapping in self.resolved_mappings_:
            smooth_name = mapping.smooth_name
            smooth_layer = mapping.smooth_layer
            balance_layers = mapping.balance_layers
            
            print(f"\n📍 处理映射: {smooth_name}")
            print(f"   平滑层: {type(smooth_layer).__name__}")
            print(f"   平衡层数量: {len(balance_layers)}")
            
            # 获取激活动态范围
            if smooth_name in self.scales_:
                activation_scales = (
                    self.scales_[smooth_name].max_channel_vals
                    - self.scales_[smooth_name].min_channel_vals
                )
                
                print(f"   激活动态范围: [{activation_scales.min():.6f}, {activation_scales.max():.6f}]")
                
                # 计算平滑因子
                smooth_scales = self._calculate_smoothing_scales(balance_layers, activation_scales)
                
                print(f"   平滑因子: [{smooth_scales.min():.6f}, {smooth_scales.max():.6f}]")
                
                # 追踪平滑因子计算
                tracker.track_scale_calculation(
                    'smoothquant_smooth_scales',
                    smooth_name,
                    {
                        'activation_scales': {
                            'shape': list(activation_scales.shape),
                            'min': activation_scales.min().item(),
                            'max': activation_scales.max().item(),
                            'mean': activation_scales.mean().item(),
                            'std': activation_scales.std().item()
                        },
                        'smooth_scales': {
                            'shape': list(smooth_scales.shape),
                            'min': smooth_scales.min().item(),
                            'max': smooth_scales.max().item(),
                            'mean': smooth_scales.mean().item(),
                            'std': smooth_scales.std().item()
                        }
                    }
                )
                
                # 应用平滑变换
                @torch.no_grad()
                def smooth(module):
                    # 确保模块在正确的设备上
                    if module in balance_layers:
                        # Linear层权重变换
                        old_weight_std = module.weight.std().item()
                        module.weight.mul_(smooth_scales.view(1, -1))
                        new_weight_std = module.weight.std().item()
                        
                        print(f"     {type(module).__name__}: std {old_weight_std:.6f} -> {new_weight_std:.6f}")
                        
                        # 追踪权重变换
                        tracker.track_smoothquant_operation(
                            'weight_transformation',
                            f"{smooth_name}_balance",
                            {
                                'module_type': type(module).__name__,
                                'weight_shape': list(module.weight.shape),
                                'old_std': old_weight_std,
                                'new_std': new_weight_std,
                                'std_change_ratio': new_weight_std / old_weight_std,
                                'smooth_scales_applied': {
                                    'min': smooth_scales.min().item(),
                                    'max': smooth_scales.max().item()
                                }
                            }
                        )
                        
                    elif module == smooth_layer:
                        # LayerNorm层权重变换
                        old_weight_mean = module.weight.mean().item()
                        if module.weight.ndim == 1:
                            module.weight.div_(smooth_scales)
                        else:
                            module.weight.div_(smooth_scales.view(-1, 1))
                        new_weight_mean = module.weight.mean().item()
                        
                        print(f"     {type(module).__name__}: mean {old_weight_mean:.6f} -> {new_weight_mean:.6f}")
                        
                        # 追踪LayerNorm变换
                        tracker.track_smoothquant_operation(
                            'layernorm_transformation',
                            smooth_name,
                            {
                                'module_type': type(module).__name__,
                                'weight_shape': list(module.weight.shape),
                                'old_mean': old_weight_mean,
                                'new_mean': new_weight_mean,
                                'mean_change_ratio': new_weight_mean / old_weight_mean if old_weight_mean != 0 else 0,
                                'smooth_scales_applied': {
                                    'min': smooth_scales.min().item(),
                                    'max': smooth_scales.max().item()
                                }
                            }
                        )
                
                # 应用到所有相关模块
                for module in [smooth_layer] + balance_layers:
                    smooth(module)
                
                # 清理统计数据
                del self.scales_[smooth_name]
        
        return original_apply_smoothing(self, model)
    
    # 应用补丁
    SmoothQuantModifier._setup_scale_hooks = tracked_setup_scale_hooks
    SmoothQuantModifier._apply_smoothing = tracked_apply_smoothing
    
    print("✅ SmoothQuant详细追踪补丁已应用")

def patch_gptq_for_detailed_tracking():
    """给GPTQ打详细追踪补丁"""
    from llmcompressor.modifiers.quantization.gptq.base import GPTQModifier
    from llmcompressor.modifiers.quantization.gptq.gptq_quantize import accumulate_hessian
    from compressed_tensors.quantization import QuantizationStrategy

    # 1. 追踪Hessian累积
    original_accumulate_hessian = accumulate_hessian

    def tracked_accumulate_hessian(inp, module, H, num_samples):
        module_name = str(type(module).__name__)

        print(f"\n🔍 Hessian累积: {module_name}")
        print(f"   输入形状: {inp.shape}")
        print(f"   Hessian形状: {H.shape}")
        print(f"   当前样本数: {num_samples}")

        # 执行原始累积
        result_H, result_samples = original_accumulate_hessian(inp, module, H, num_samples)

        print(f"   累积后样本数: {result_samples}")
        print(f"   Hessian对角线范围: [{torch.diag(result_H).min():.6f}, {torch.diag(result_H).max():.6f}]")

        # 追踪Hessian操作
        tracker.hessian_operations.append({
            'timestamp': time.time(),
            'module_name': module_name,
            'input_shape': list(inp.shape),
            'hessian_shape': list(H.shape),
            'num_samples_before': num_samples,
            'num_samples_after': result_samples,
            'hessian_diag_stats': {
                'min': torch.diag(result_H).min().item(),
                'max': torch.diag(result_H).max().item(),
                'mean': torch.diag(result_H).mean().item(),
                'std': torch.diag(result_H).std().item()
            }
        })

        return result_H, result_samples

    # 2. 追踪块级量化过程
    def create_block_tracking_quantize_weight():
        """创建带块级追踪的量化函数"""

        def tracked_quantize_weight_with_blocks(module, quant_args, hessians_dict, blocksize, percdamp):
            module_name = str(type(module).__name__)

            print(f"\n🔧 权重量化开始: {module_name}")
            print(f"   量化策略: {quant_args.strategy}")
            print(f"   块大小: {blocksize}")
            print(f"   阻尼系数: {percdamp}")
            print(f"   权重形状: {module.weight.shape}")

            # 获取权重和Hessian
            W = module.weight.data.clone()
            if module in hessians_dict:
                H = hessians_dict[module]
                print(f"   Hessian形状: {H.shape}")
                print(f"   Hessian条件数: {torch.linalg.cond(H).item():.2e}")
            else:
                print(f"   ❌ 未找到Hessian矩阵!")
                return None, None, None, None, None

            # 记录量化前统计
            weight_stats_before = {
                'mean': W.mean().item(),
                'std': W.std().item(),
                'min': W.min().item(),
                'max': W.max().item()
            }

            print(f"   量化前权重统计: mean={weight_stats_before['mean']:.6f}, "
                  f"std={weight_stats_before['std']:.6f}")

            # 🔥 模拟块级量化过程（简化版）
            num_rows, num_columns = W.shape
            num_blocks = (num_columns + blocksize - 1) // blocksize

            print(f"\n🔍 块级量化分析:")
            print(f"   总列数: {num_columns}")
            print(f"   块大小: {blocksize}")
            print(f"   块数量: {num_blocks}")

            # 计算per-channel scale (Observer方法)
            from llmcompressor.observers.base import Observer
            observer = Observer.load_from_registry(
                quant_args.observer,
                quantization_args=quant_args,
                averaging_constant=1.0
            )

            # 🔥 关键：per-channel scale计算
            if quant_args.strategy == QuantizationStrategy.CHANNEL:
                scale, zero_point = observer(W, reduce_dims=(1,))  # 按行(输出通道)
                print(f"   Per-channel scale形状: {scale.shape}")
                print(f"   Per-channel scale范围: [{scale.min():.8f}, {scale.max():.8f}]")
            else:
                scale, zero_point = observer(W)
                print(f"   Per-tensor scale: {scale.item():.8f}")

            # 🔥 追踪每个块的处理
            block_details = []
            for block_idx in range(num_blocks):
                i1 = block_idx * blocksize
                i2 = min(i1 + blocksize, num_columns)
                block_size = i2 - i1

                # 当前块的权重
                W_block = W[:, i1:i2]  # [num_rows, block_size]

                # 当前块的统计
                block_stats = {
                    'block_idx': block_idx,
                    'start_col': i1,
                    'end_col': i2,
                    'block_size': block_size,
                    'weight_stats': {
                        'mean': W_block.mean().item(),
                        'std': W_block.std().item(),
                        'min': W_block.min().item(),
                        'max': W_block.max().item()
                    }
                }

                print(f"   块 {block_idx}: 列[{i1}:{i2}], 大小={block_size}")
                print(f"     权重统计: mean={block_stats['weight_stats']['mean']:.6f}, "
                      f"std={block_stats['weight_stats']['std']:.6f}")

                # 🔥 关键：在块级优化中应用per-channel scale
                if quant_args.strategy == QuantizationStrategy.CHANNEL:
                    # 每一行(输出通道)使用自己的scale
                    for row_idx in range(num_rows):
                        row_scale = scale[row_idx, 0]
                        row_weight = W_block[row_idx, :]

                        # 模拟量化过程
                        quantized_row = torch.round(row_weight / row_scale).clamp(-128, 127)
                        quantization_error = torch.abs(row_weight - quantized_row * row_scale).mean()

                        if block_idx == 0 and row_idx < 3:  # 只显示前几行的详细信息
                            print(f"       行{row_idx}: scale={row_scale:.8f}, "
                                  f"量化误差={quantization_error:.8f}")

                block_details.append(block_stats)

                # 追踪块操作
                tracker.track_block_operation(
                    f"{module_name}_block_{block_idx}",
                    block_stats
                )

            # 执行原始量化获取最终结果
            from llmcompressor.modifiers.quantization.gptq.gptq_quantize import quantize_weight as original_quantize_weight
            loss, quantized_weight, final_scale, final_zero_point, g_idx = original_quantize_weight(
                module, quant_args, hessians_dict, blocksize, percdamp
            )

            print(f"\n📊 最终量化结果:")
            print(f"   量化损失: {loss:.8f}")
            print(f"   最终scale形状: {final_scale.shape}")
            print(f"   最终scale范围: [{final_scale.min():.8f}, {final_scale.max():.8f}]")

            # 🔥 对比分析：Observer scale vs 最终scale
            if quant_args.strategy == QuantizationStrategy.CHANNEL:
                scale_diff = torch.abs(scale - final_scale).mean()
                print(f"   Observer scale vs 最终scale差异: {scale_diff:.8f}")
                print(f"   差异百分比: {(scale_diff / scale.mean() * 100):.4f}%")

                if scale_diff / scale.mean() > 0.01:  # 差异超过1%
                    print(f"   ⚠️  块优化显著改变了scale值!")
                else:
                    print(f"   ✅ 块优化对scale影响较小")

            # 追踪最终结果
            tracker.track_gptq_operation(
                'weight_quantization_with_blocks',
                module_name,
                {
                    'quantization_strategy': str(quant_args.strategy),
                    'block_size': blocksize,
                    'num_blocks': num_blocks,
                    'weight_shape': list(W.shape),
                    'weight_stats_before': weight_stats_before,
                    'quantization_loss': loss,
                    'observer_scale_stats': {
                        'shape': list(scale.shape),
                        'min': scale.min().item(),
                        'max': scale.max().item(),
                        'mean': scale.mean().item()
                    },
                    'final_scale_stats': {
                        'shape': list(final_scale.shape),
                        'min': final_scale.min().item(),
                        'max': final_scale.max().item(),
                        'mean': final_scale.mean().item()
                    },
                    'scale_difference': {
                        'absolute_diff': torch.abs(scale - final_scale).mean().item() if quant_args.strategy == QuantizationStrategy.CHANNEL else 0,
                        'relative_diff_percent': (torch.abs(scale - final_scale).mean() / scale.mean() * 100).item() if quant_args.strategy == QuantizationStrategy.CHANNEL else 0
                    },
                    'block_details': block_details
                }
            )

            return loss, quantized_weight, final_scale, final_zero_point, g_idx

        return tracked_quantize_weight_with_blocks
    
    # 应用补丁
    import llmcompressor.modifiers.quantization.gptq.gptq_quantize as gptq_module
    gptq_module.accumulate_hessian = tracked_accumulate_hessian
    gptq_module.quantize_weight = create_block_tracking_quantize_weight()

    print("✅ GPTQ详细追踪补丁已应用")

def test_detailed_quantization_flow():
    """测试详细的量化流程"""
    print("🚀 开始详细量化流程测试")
    print("="*80)
    
    # 应用追踪补丁
    patch_smoothquant_for_detailed_tracking()
    patch_gptq_for_detailed_tracking()
    
    # 加载模型
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(MODEL_ID, torch_dtype=torch.float16)
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(8))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=128, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 创建recipe
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    print(f"📋 Recipe配置:")
    for i, modifier in enumerate(recipe, 1):
        print(f"   {i}. {type(modifier).__name__}")
    
    # 执行量化
    print(f"\n🔄 执行量化...")
    from llmcompressor.entrypoints.oneshot import oneshot
    
    try:
        quantized_model = oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=128,
            num_calibration_samples=8,
        )
        
        print(f"✅ 量化成功完成!")
        
        # 分析追踪结果
        analyze_detailed_tracking_results()
        
        return quantized_model
        
    except Exception as e:
        print(f"❌ 量化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_detailed_tracking_results():
    """分析详细的追踪结果"""
    print(f"\n📊 详细追踪结果分析")
    print("="*80)

    global tracker

    # 1. SmoothQuant操作分析
    print(f"\n🔍 SmoothQuant操作分析:")
    print(f"   总操作数: {len(tracker.smoothquant_operations)}")

    activation_stats = [op for op in tracker.smoothquant_operations if op['operation_type'] == 'activation_stats_collection']
    weight_transforms = [op for op in tracker.smoothquant_operations if op['operation_type'] == 'weight_transformation']
    layernorm_transforms = [op for op in tracker.smoothquant_operations if op['operation_type'] == 'layernorm_transformation']

    print(f"   激活统计收集: {len(activation_stats)} 次")
    print(f"   权重变换: {len(weight_transforms)} 次")
    print(f"   LayerNorm变换: {len(layernorm_transforms)} 次")

    # 2. GPTQ操作分析
    print(f"\n🔍 GPTQ操作分析:")
    print(f"   总操作数: {len(tracker.gptq_operations)}")
    print(f"   Hessian操作数: {len(tracker.hessian_operations)}")
    print(f"   块操作数: {len(tracker.block_operations)}")

    weight_quantizations = [op for op in tracker.gptq_operations if op['operation_type'] == 'weight_quantization_with_blocks']
    print(f"   权重量化(带块分析): {len(weight_quantizations)} 次")

    # 3. 块级vs通道级冲突分析
    print(f"\n🔍 块级vs通道级冲突分析:")
    if weight_quantizations:
        for i, quant_op in enumerate(weight_quantizations, 1):
            layer_name = quant_op['layer_name']
            data = quant_op['data']

            print(f"\n   {i}. {layer_name}:")
            print(f"      量化策略: {data['quantization_strategy']}")
            print(f"      块大小: {data['block_size']}")
            print(f"      块数量: {data['num_blocks']}")
            print(f"      权重形状: {data['weight_shape']}")

            # 🔥 关键：scale差异分析
            if 'scale_difference' in data:
                abs_diff = data['scale_difference']['absolute_diff']
                rel_diff = data['scale_difference']['relative_diff_percent']

                print(f"      Observer scale vs 最终scale:")
                print(f"        绝对差异: {abs_diff:.8f}")
                print(f"        相对差异: {rel_diff:.4f}%")

                if rel_diff > 1.0:
                    print(f"        ⚠️  块优化显著改变了scale值!")
                elif rel_diff > 0.1:
                    print(f"        ⚠️  块优化轻微改变了scale值")
                else:
                    print(f"        ✅ 块优化对scale影响很小")

            # 量化损失分析
            print(f"      量化损失: {data['quantization_loss']:.8f}")

            # 块级详细信息
            if 'block_details' in data and len(data['block_details']) > 0:
                print(f"      块级统计 (前3块):")
                for j, block in enumerate(data['block_details'][:3]):
                    print(f"        块{j}: 列[{block['start_col']}:{block['end_col']}], "
                          f"std={block['weight_stats']['std']:.6f}")

    # 4. Scale计算分析
    print(f"\n🔍 Scale计算分析:")
    print(f"   总scale计算: {len(tracker.scale_calculations)}")

    smoothquant_scales = [calc for calc in tracker.scale_calculations if calc['calculation_type'] == 'smoothquant_smooth_scales']

    print(f"   SmoothQuant平滑因子: {len(smoothquant_scales)} 次")

    if smoothquant_scales:
        print(f"\n📈 SmoothQuant平滑因子详细分析:")
        for i, scale_calc in enumerate(smoothquant_scales, 1):
            layer_name = scale_calc['layer_name']
            scale_data = scale_calc['scale_data']

            print(f"   {i}. {layer_name}:")
            print(f"      激活动态范围: [{scale_data['activation_scales']['min']:.6f}, "
                  f"{scale_data['activation_scales']['max']:.6f}]")
            print(f"      平滑因子范围: [{scale_data['smooth_scales']['min']:.6f}, "
                  f"{scale_data['smooth_scales']['max']:.6f}]")

    # 5. 生成详细报告
    generate_detailed_report()

def generate_detailed_report():
    """生成详细的量化流程报告"""
    global tracker
    
    report_path = "/workspace/smoothquant_gptq_detailed_analysis_report.md"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# SmoothQuant + GPTQ 详细量化流程分析报告\n\n")
        f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # SmoothQuant分析
        f.write("## 1. SmoothQuant操作详细分析\n\n")
        f.write(f"总操作数: {len(tracker.smoothquant_operations)}\n\n")
        
        activation_stats = [op for op in tracker.smoothquant_operations if op['operation_type'] == 'activation_stats_collection']
        weight_transforms = [op for op in tracker.smoothquant_operations if op['operation_type'] == 'weight_transformation']
        layernorm_transforms = [op for op in tracker.smoothquant_operations if op['operation_type'] == 'layernorm_transformation']
        
        f.write(f"- 激活统计收集: {len(activation_stats)} 次\n")
        f.write(f"- 权重变换: {len(weight_transforms)} 次\n")
        f.write(f"- LayerNorm变换: {len(layernorm_transforms)} 次\n\n")
        
        # GPTQ分析
        f.write("## 2. GPTQ操作详细分析\n\n")
        f.write(f"总操作数: {len(tracker.gptq_operations)}\n")
        f.write(f"Hessian操作数: {len(tracker.hessian_operations)}\n")
        f.write(f"块操作数: {len(tracker.block_operations)}\n\n")
        
        weight_quantizations = [op for op in tracker.gptq_operations if op['operation_type'] == 'weight_quantization_with_blocks']
        f.write(f"权重量化(带块分析): {len(weight_quantizations)} 次\n\n")
        
        # Scale处理分析
        f.write("## 3. Scale处理和冲突解决\n\n")
        f.write("### Per-Channel vs Per-Block冲突分析\n\n")
        
        for i, quant_op in enumerate(weight_quantizations, 1):
            layer_name = quant_op['layer_name']
            data = quant_op['data']
            
            f.write(f"#### {i}. {layer_name}\n\n")
            f.write(f"- 量化策略: {data['quantization_strategy']}\n")
            f.write(f"- 块大小: {data['block_size']}\n")
            f.write(f"- 块数量: {data['num_blocks']}\n")
            f.write(f"- 权重形状: {data['weight_shape']}\n\n")
            
            if 'scale_difference' in data:
                abs_diff = data['scale_difference']['absolute_diff']
                rel_diff = data['scale_difference']['relative_diff_percent']
                
                f.write(f"**Scale差异分析:**\n")
                f.write(f"- 绝对差异: {abs_diff:.8f}\n")
                f.write(f"- 相对差异: {rel_diff:.4f}%\n")
                
                if rel_diff > 1.0:
                    f.write(f"- 结论: ⚠️ 块优化显著改变了scale值!\n\n")
                elif rel_diff > 0.1:
                    f.write(f"- 结论: ⚠️ 块优化轻微改变了scale值\n\n")
                else:
                    f.write(f"- 结论: ✅ 块优化对scale影响很小\n\n")
        
        f.write("## 4. 技术总结\n\n")
        f.write("1. SmoothQuant通过激活统计收集和权重平滑预处理模型\n")
        f.write("2. GPTQ使用Hessian矩阵进行块级优化的权重量化\n")
        f.write("3. Scale计算在per-channel和per-block间通过工程妥协解决冲突\n")
        f.write("4. 最终的scale格式完全兼容vLLM推理引擎\n\n")
    
    print(f"✅ 详细报告已生成: {report_path}")

def main():
    """主函数"""
    print("🔍 SmoothQuant + GPTQ详细量化流程分析")
    print("="*80)
    
    # 执行详细测试
    quantized_model = test_detailed_quantization_flow()
    
    if quantized_model:
        print(f"\n🎉 分析完成!")
        print("="*80)
        print("关键发现:")
        print("1. SmoothQuant通过激活统计收集和权重变换预处理模型")
        print("2. GPTQ使用Hessian矩阵进行块级优化的权重量化")
        print("3. Scale计算在per-channel和per-block间通过工程妥协解决冲突")
        print("4. 最终的scale格式完全兼容vLLM推理引擎")

if __name__ == "__main__":
    main()
