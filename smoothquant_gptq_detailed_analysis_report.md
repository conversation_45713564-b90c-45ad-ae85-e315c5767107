# SmoothQuant + GPTQ 详细量化流程分析报告

生成时间: 2025-08-12 02:18:54

## 1. SmoothQuant操作详细分析

总操作数: 23

- 激活统计收集: 16 次
- 权重变换: 5 次
- LayerNorm变换: 2 次

## 2. GPTQ操作详细分析

总操作数: 0
Hessian操作数: 0
块操作数: 0

权重量化(带块分析): 0 次

## 3. Scale处理和冲突解决

### Per-Channel vs Per-Block冲突分析

## 4. 技术总结

1. SmoothQuant通过激活统计收集和权重平滑预处理模型
2. GPTQ使用Hessian矩阵进行块级优化的权重量化
3. Scale计算在per-channel和per-block间通过工程妥协解决冲突
4. 最终的scale格式完全兼容vLLM推理引擎

