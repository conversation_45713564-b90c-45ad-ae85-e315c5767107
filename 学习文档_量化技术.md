
# 学习文档: 量化技术 (INT8, FP8, INT4)

本文档深入探讨vLLM和LLM Compressor中支持的几种关键量化技术。

---

## 1. INT8, FP8, INT4 基础

### 1.1. 基本概念

*   **量化 (Quantization):** 将高精度（如FP32, FP16）的浮点数转换为低精度（如INT8, INT4）的整数或浮点数的过程。
    *   **优点:** 减少模型大小、降低显存占用、加速计算（如果硬件支持）。
    *   **挑战:** 精度损失。
*   **数据类型:**
    *   **FP16 (Half Precision):** 1个符号位, 5个指数位, 10个尾数位。动态范围较大，是目前模型训练和推理的基准。
    *   **INT8 (8-bit Integer):** 8位整数，表示范围-128到127。需要缩放因子（scale）来映射回浮点数范围。
    *   **FP8 (8-bit Floating Point):** 8位浮点数，有两种主流格式：
        *   **E5M2:** 1个符号位, 5个指数位, 2个尾数位。动态范围与FP16类似，但精度较低。适合表示权重。
        *   **E4M3:** 1个符号位, 4个指数位, 3个尾数位。动态范围较小，但精度较高。适合表示激活和梯度。
        *   **硬件支持:** NVIDIA Hopper和Blackwell架构的Tensor Core原生支持FP8计算，能带来显著的性能提升。
    *   **INT4 (4-bit Integer):** 4位整数，表示范围-8到7。极大地压缩模型，但精度损失风险更高。

### 1.2. 核心公式

量化的核心是以下映射关系：
`float_value ≈ int_value * scale`

*   **非对称量化 (Asymmetric):** `float_value ≈ (int_value - zero_point) * scale`
    *   `zero_point` 是一个整数，用于映射浮点数的0点。
*   **对称量化 (Symmetric):** `float_value ≈ int_value * scale`
    *   `zero_point` 通常为0，适用于权重等分布在0附近的张量。

---

## 2. vLLM 中的量化实现

vLLM 的设计哲学是**为推理而生**，因此它的量化模块专注于**加载和执行**已经量化好的模型。

**核心源码路径:** `vllm/model_executor/layers/quantization/`

### 2.1. AWQ (Activation-aware Weight Quantization)

*   **原理:** AWQ 认为权重的重要性并非均等，一些显著的权重（salient weights）对模型性能至关重要。它通过观察激活值的分布，找到一个合适的缩放因子来保护这些重要权重，从而在量化时获得更好的性能。这是一种**per-channel**的缩放策略。
*   **vLLM 源码分析 (`awq.py`):**
    *   `AWQLinearMethod` 类继承自 `QuantizationMethod`。
    *   `apply` 函数是核心，它接收一个 `nn.Module` (通常是 `nn.Linear`)，并将其替换为支持AWQ的量化线性层。
    *   **Kernel选择:** vLLM会根据模型配置和硬件能力，选择不同的底层计算核：
        *   `AWQ_MARLIN_MIN_THREAD_N`: 如果满足条件，会使用 `Marlin` 提供的高性能INT4/INT8混合精度Kernel。
        *   `awq_triton_kernl`: 否则，会使用基于Triton语言编写的Kernel。
    *   **代码示例 (简化的 `apply` 逻辑):**
        ```python
        # In vllm/model_executor/layers/quantization/awq.py
        class AWQLinearMethod(QuantizationMethod):
            def apply(self, module: nn.Module, ...) -> nn.Module:
                # ...
                # 1. 获取量化参数 (qweight, qzeros, scales)
                qweight = module.qweight
                qzeros = module.qzeros
                scales = module.scales
                
                # 2. 根据条件选择并创建一个量化线性层
                if use_marlin:
                    return MarlinLinear(..., qweight, qzeros, scales)
                else:
                    return AWQLinear(..., qweight, qzeros, scales)
        ```

### 2.2. GPTQ (Generative Pre-trained Transformer Quantization)

*   **原理:** GPTQ 是一种基于重构误差的量化方法。它逐层对权重进行量化，并更新后续未量化的权重，以补偿量化带来的误差。这使得GPTQ在低比特（如4-bit）下也能保持较好的精度。
*   **vLLM 源码分析 (`gptq.py`):**
    *   `GPTQLinearMethod` 同样继承自 `QuantizationMethod`。
    *   vLLM的GPTQ实现严重依赖于外部的高性能Kernel库，如 `AutoGPTQ` 的 `exllama` 或 `Marlin`。
    *   `apply` 函数的逻辑是加载GPTQ格式的权重（通常包含 `qweight`, `qzeros`, `scales`, `g_idx`），然后用一个高性能的量化线性层替换原始的 `nn.Linear`。
    *   **代码示例 (简化的 `apply` 逻辑):**
        ```python
        # In vllm/model_executor/layers/quantization/gptq.py
        class GPTQLinearMethod(QuantizationMethod):
            def apply(self, module: nn.Module, ...) -> nn.Module:
                # ...
                # 1. 检查是否安装了所需的kernel (ex: exllama)
                # raise ImportError("exllama is not installed.")
                
                # 2. 从state_dict中加载GPTQ参数
                qweight = module.qweight
                # ... 其他参数
                
                # 3. 创建一个QuantLinear层，该层在forward时会调用高性能kernel
                return QuantLinear(..., qweight, ...)
        ```

### 2.3. FP8 (8-bit Floating Point)

*   **原理:** 直接利用硬件（Hopper/Blackwell）的FP8计算能力。与INT8不同，FP8本身就是浮点数，但动态范围有限。因此，**缩放因子 (scaling factor)** 同样至关重要。
*   **vLLM 源码分析 (`fp8.py`, `fp8_utils.py`):**
    *   `FP8LinearMethod`: 用于处理FP8权重的量化方法。
    *   `fp8_utils.py`: 包含了计算和应用缩放因子的核心逻辑。
        *   `FP8ScalingTarget`: 定义了缩放因子作用的目标，可以是 `WEIGHT`（权重）或 `ACTIVATION`（激活）。
        *   `get_fp8_scale_manager`: 获取一个上下文管理器，用于在`forward`传递期间自动应用缩放。
    *   **动态缩放:** vLLM支持动态FP8缩放。在每次前向传播时，会收集激活值的`amax`（绝对值最大值），然后用它来更新下一次迭代的缩放因子，以防止溢出或精度不足。
    *   **代码示例 (Transformer中FP8的应用):**
        ```python
        # 伪代码，演示在Transformer层中如何使用FP8
        from vllm.model_executor.layers.quantization.utils.fp8_utils import get_fp8_scale_manager

        class TransformerLayer(nn.Module):
            def forward(self, hidden_states):
                # ...
                # 进入FP8计算区域，激活值会被缩放
                with get_fp8_scale_manager(hidden_states, target=FP8ScalingTarget.ACTIVATION):
                    # 在这个上下文中，hidden_states被转换为FP8
                    # self.qkv_proj是一个FP8线性层
                    qkv = self.qkv_proj(hidden_states) 
                # ...
                # 退出上下文后，数据可能被转换回FP16
        ```

### 2.4. Marlin (INT4)

*   **原理:** Marlin是NVIDIA开发的一个针对4-bit量化的高性能GEMM Kernel。它通过巧妙的数据重排和共享内存使用，实现了比传统方法快得多的INT4推理。
*   **vLLM 源码分析 (`marlin.py`):**
    *   `MarlinLinear`: 这是Marlin Kernel的PyTorch封装。它接收INT4量化的权重和缩放因子。
    *   `MarlinLinearMethod`: 当检测到模型是Marlin兼容的格式时，vLLM会用这个方法将`nn.Linear`替换为`MarlinLinear`。
    *   Marlin通常与AWQ或GPTQ格式的模型一起使用，作为其后端加速引擎。

---

## 3. LLM Compressor 中的量化实现

LLM Compressor 的定位是**生成量化模型**的工具集，它提供了实现各种量化算法的`Modifier`。

**核心源码路径:** `llmcompressor/modifiers/`

### 3.1. `QuantizationModifier`

*   **核心文件:** `llmcompressor/modifiers/quantization/quantization/base.py`
*   **原理:** 这是一个通用的量化修改器，通过`recipe.yaml`文件进行配置。它可以指定哪些层需要量化、量化的比特数、对称/非对称等。
*   **Recipe 示例:**
    ```yaml
    quant_stage:
        QuantizationModifier:
            ignore: ["lm_head"] # 不量化lm_head层
            config_groups:
                group_1:
                    weights:
                        num_bits: 8
                        type: "int"
                        symmetric: True
                    input_activations: null # 不量化输入激活
                    output_activations: null
            targets: ["Linear"] # 只对线性层应用
    ```

### 3.2. `GPTQModifier`

*   **核心文件:** `llmcompressor/modifiers/quantization/gptq/base.py`
*   **原理:** 提供了在LLM Compressor框架内执行GPTQ算法的能力。
    *   `gptq_quantize.py`: 包含了GPTQ量化的核心实现，包括Hessian矩阵的计算和权重的逐列更新。
    *   它是一个`one-shot`过程，需要一小部分校准数据。
*   **Recipe 示例:**
    ```yaml
    gptq_quant_stage:
        GPTQModifier:
            block_size: 128
            per_channel: True
            targets: ["LlamaDecoderLayer"] # 在Decoder层级别上应用
    ```

### 3.3. `SmoothQuantModifier`

*   **核心文件:** `llmcompressor/modifiers/smoothquant/base.py`
*   **原理:** SmoothQuant 旨在解决激活值量化困难的问题。在一些激活值中，可能存在一些离群点（outliers），导致动态范围过大，量化后精度损失严重。SmoothQuant通过一个数学上等价的变换，将激活值的量化难度“平滑”或“迁移”一部分到权重上。
    *   `X * W = (X * diag(s)) * (diag(s)^-1 * W) = X' * W'`
    *   它计算一个缩放因子`s`，使得`X'`更容易量化，而`W'`的量化难度在可接受范围内。
*   **LLM Compressor 实现:**
    *   `SmoothQuantModifier`通常在`QuantizationModifier`之前运行。
    *   它会遍历模型，找到`Linear`层和它们前面的`LayerNorm`，然后计算并应用平滑缩放因子。

---

## 4. 总结与对比

| 特性 | vLLM | LLM Compressor |
| :--- | :--- | :--- |
| **定位** | 高性能**推理引擎** | 模型**压缩工具集** |
| **核心功能** | 加载和执行量化模型 | 生成量化/稀疏模型 |
| **量化时机** | 运行时 (Inference) | 离线 (Offline) |
| **主要抽象** | `QuantizationMethod` | `Modifier` |
| **支持的算法** | 侧重于推理加速 (AWQ, GPTQ, FP8, Marlin) | 侧重于算法实现 (SmoothQuant, GPTQ, Pruning) |
| **工作流程** | `EngineArgs` -> `LLMEngine` | `Recipe` -> `oneshot`/`train` -> Exported Model |

**协同工作流程:**

1.  使用 **LLM Compressor** 和一个 `recipe.yaml` 文件对一个FP16模型进行压缩（例如，应用SmoothQuant + Quantization）。
2.  将压缩后的模型和配置文件（`quantization_config.json`）保存下来。
3.  编写 **vLLM** 推理脚本，使用 `LLMEngine` 加载上一步保存的模型。
4.  vLLM的 `QuantizationMethod` 会解析 `quantization_config.json`，并自动选择合适的底层Kernel来执行推理。
