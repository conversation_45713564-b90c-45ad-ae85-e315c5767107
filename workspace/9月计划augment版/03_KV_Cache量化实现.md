# KV Cache量化实现深度学习文档

## 学习时间：9.8-9.9 (2天)

## 学习目标

1. 深入理解KV Cache的内存布局和管理机制
2. 掌握KV Cache量化的数学原理和实现细节
3. 分析量化缩放因子的计算、存储和使用方法
4. 对比动态量化与静态量化在KV Cache中的应用
5. 理解注意力机制中量化的集成方式

## KV Cache基础原理

### 1. KV Cache的作用机制

在Transformer解码过程中，KV Cache用于存储之前计算的Key和Value张量，避免重复计算：

```
# 传统方式（每次重新计算）
for i in range(seq_len):
    Q_i = input[i] @ W_q
    K_all = input[:i+1] @ W_k  # 重复计算
    V_all = input[:i+1] @ W_v  # 重复计算
    output[i] = attention(Q_i, K_all, V_all)

# KV Cache方式（增量计算）
K_cache, V_cache = [], []
for i in range(seq_len):
    Q_i = input[i] @ W_q
    K_i = input[i] @ W_k
    V_i = input[i] @ W_v
    
    K_cache.append(K_i)  # 缓存Key
    V_cache.append(V_i)  # 缓存Value
    
    output[i] = attention(Q_i, K_cache, V_cache)
```

### 2. KV Cache的内存挑战

KV Cache的内存占用随序列长度线性增长：
- **内存占用**: `batch_size × num_layers × num_heads × seq_len × head_dim × 2 × sizeof(dtype)`
- **长序列问题**: 对于长序列（如32K tokens），KV Cache可能占用数十GB内存
- **量化必要性**: 通过量化可以显著减少内存占用

## vLLM中的KV Cache量化实现

### 1. BaseKVCacheMethod核心设计

```python
# 来源: vllm/model_executor/layers/quantization/kv_cache.py
class BaseKVCacheMethod(QuantizeMethodBase):
    """
    KV Cache量化方法基类
    添加 _k_scale 和 _v_scale 属性到注意力层
    用于支持从检查点加载量化缩放因子
    """

    def __init__(self, quant_config: QuantizationConfig):
        self.quant_config = quant_config

    def create_weights(self, layer: torch.nn.Module):
        """
        为注意力层创建量化缩放因子参数
        """
        # 初始化Q、K、V的缩放因子为-1.0（无效值）
        # 如果检查点中存在缩放因子，加载时会被覆盖
        layer.q_scale = torch.nn.Parameter(torch.tensor(-1.0), requires_grad=False)
        layer.k_scale = torch.nn.Parameter(torch.tensor(-1.0), requires_grad=False)
        layer.v_scale = torch.nn.Parameter(torch.tensor(-1.0), requires_grad=False)
        
        # 初始化注意力概率P = softmax(QK^T)的缩放因子
        layer.prob_scale = torch.nn.Parameter(torch.tensor(-1.0), requires_grad=False)

    def apply(self, layer: torch.nn.Module) -> torch.Tensor:
        """BaseKVCacheMethod不应该被直接调用apply方法"""
        raise RuntimeError(f"{self.__class__.__name__}.apply should not be called.")

    def process_weights_after_loading(self, layer: torch.nn.Module) -> None:
        """权重加载后的处理，验证缩放因子是否正确加载"""
        # 检查缩放因子是否从检查点正确加载
        if hasattr(layer, 'k_scale') and layer.k_scale.item() == -1.0:
            logger.warning(f"Layer {layer} 的 k_scale 未从检查点加载，使用默认值")
        if hasattr(layer, 'v_scale') and layer.v_scale.item() == -1.0:
            logger.warning(f"Layer {layer} 的 v_scale 未从检查点加载，使用默认值")
```

### 2. FP8 KV Cache量化实现

```python
# 来源: vllm/model_executor/layers/quantization/fp8.py (KV Cache相关部分)
class Fp8KVCacheMethod(BaseKVCacheMethod):
    """FP8 KV Cache量化实现"""
    
    def __init__(self, quant_config: Fp8Config):
        super().__init__(quant_config)
        self.activation_scheme = quant_config.activation_scheme
    
    def quantize_kv_cache(self, key: torch.Tensor, value: torch.Tensor, 
                         k_scale: torch.Tensor, v_scale: torch.Tensor):
        """量化Key和Value张量"""
        
        if self.activation_scheme == "dynamic":
            # 动态量化：实时计算缩放因子
            k_scale = self._compute_dynamic_scale(key)
            v_scale = self._compute_dynamic_scale(value)
        
        # 执行FP8量化
        key_quantized = (key / k_scale).to(torch.float8_e4m3fn)
        value_quantized = (value / v_scale).to(torch.float8_e4m3fn)
        
        return key_quantized, value_quantized, k_scale, v_scale
    
    def dequantize_kv_cache(self, key_quantized: torch.Tensor, 
                           value_quantized: torch.Tensor,
                           k_scale: torch.Tensor, v_scale: torch.Tensor):
        """反量化Key和Value张量"""
        key = key_quantized.to(torch.float16) * k_scale
        value = value_quantized.to(torch.float16) * v_scale
        return key, value
    
    def _compute_dynamic_scale(self, tensor: torch.Tensor) -> torch.Tensor:
        """计算动态量化缩放因子"""
        # 计算张量的最大绝对值
        amax = torch.max(torch.abs(tensor))
        # FP8 E4M3格式的最大值
        fp8_max = torch.finfo(torch.float8_e4m3fn).max
        # 计算缩放因子
        scale = amax / fp8_max
        return scale.clamp(min=1e-12)  # 避免除零
```

### 3. 注意力层中的KV Cache量化集成

```python
# 来源: vllm/attention/layer.py (简化版本)
class Attention(nn.Module):
    """集成KV Cache量化的注意力层"""
    
    def __init__(self, 
                 num_heads: int,
                 head_size: int,
                 scale: float,
                 num_kv_heads: Optional[int] = None,
                 alibi_slopes: Optional[List[float]] = None,
                 cache_config: Optional[CacheConfig] = None,
                 quant_config: Optional[QuantizationConfig] = None,
                 blocksparse_params: Optional[Dict[str, Any]] = None,
                 logits_soft_cap: Optional[float] = None,
                 prefix: str = ""):
        
        super().__init__()
        self.num_heads = num_heads
        self.head_size = head_size
        self.scale = scale
        self.num_kv_heads = num_kv_heads or num_heads
        
        # KV Cache量化配置
        if quant_config and hasattr(quant_config, 'kv_cache_dtype'):
            self.kv_cache_dtype = quant_config.kv_cache_dtype
            # 创建量化方法实例
            if isinstance(quant_config, Fp8Config):
                self.kv_quant_method = Fp8KVCacheMethod(quant_config)
                self.kv_quant_method.create_weights(self)
        else:
            self.kv_cache_dtype = cache_config.cache_dtype
            self.kv_quant_method = None
    
    def forward(self,
                query: torch.Tensor,
                key: torch.Tensor,
                value: torch.Tensor,
                kv_cache: torch.Tensor,
                attn_metadata: AttentionMetadata) -> torch.Tensor:
        """前向传播，集成KV Cache量化"""
        
        # 如果启用KV Cache量化
        if self.kv_quant_method is not None:
            # 量化新的Key和Value
            key_quantized, value_quantized, k_scale, v_scale = \
                self.kv_quant_method.quantize_kv_cache(
                    key, value, self.k_scale, self.v_scale)
            
            # 存储量化后的KV到缓存
            self._store_quantized_kv_cache(
                kv_cache, key_quantized, value_quantized, attn_metadata)
            
            # 从缓存中获取并反量化KV
            cached_key, cached_value = self._load_and_dequantize_kv_cache(
                kv_cache, attn_metadata, k_scale, v_scale)
        else:
            # 标准的KV Cache存储和加载
            self._store_kv_cache(kv_cache, key, value, attn_metadata)
            cached_key, cached_value = self._load_kv_cache(kv_cache, attn_metadata)
        
        # 执行注意力计算
        attn_output = self._compute_attention(
            query, cached_key, cached_value, attn_metadata)
        
        return attn_output
```

### 4. 缓存引擎中的量化支持

```python
# 来源: vllm/worker/cache_engine.py (KV Cache量化相关部分)
class CacheEngine:
    """支持量化的缓存引擎"""
    
    def __init__(self,
                 cache_config: CacheConfig,
                 model_config: ModelConfig,
                 parallel_config: ParallelConfig,
                 device_config: DeviceConfig) -> None:
        
        self.cache_config = cache_config
        self.model_config = model_config
        
        # 确定KV Cache数据类型
        self.kv_cache_dtype = self._determine_kv_cache_dtype()
        
        # 分配GPU和CPU缓存
        self.gpu_cache = self._allocate_kv_cache(
            self.cache_config.num_gpu_blocks, "cuda")
        self.cpu_cache = self._allocate_kv_cache(
            self.cache_config.num_cpu_blocks, "cpu")
    
    def _determine_kv_cache_dtype(self) -> torch.dtype:
        """确定KV Cache的数据类型"""
        if self.cache_config.cache_dtype == "auto":
            # 根据模型数据类型自动选择
            if self.model_config.dtype == torch.float16:
                return torch.float16
            else:
                return torch.bfloat16
        elif self.cache_config.cache_dtype == "fp8":
            # 使用FP8量化
            return torch.float8_e4m3fn
        elif self.cache_config.cache_dtype == "fp8_e5m2":
            return torch.float8_e5m2
        else:
            # 指定的数据类型
            return STR_DTYPE_TO_TORCH_DTYPE[self.cache_config.cache_dtype]
    
    def _allocate_kv_cache(self, num_blocks: int, device: str) -> List[torch.Tensor]:
        """分配KV Cache内存"""
        # 计算KV Cache形状
        block_size = self.cache_config.block_size
        num_heads = self.model_config.get_num_kv_heads(self.parallel_config)
        head_size = self.model_config.get_head_size()
        
        # KV Cache形状: [num_blocks, 2, num_heads, block_size, head_size]
        # 2表示Key和Value
        kv_cache_shape = (num_blocks, 2, num_heads, block_size, head_size)
        
        kv_cache = []
        for layer_idx in range(self.model_config.get_num_layers()):
            # 为每一层分配KV Cache
            layer_cache = torch.empty(
                size=kv_cache_shape,
                dtype=self.kv_cache_dtype,
                device=device,
            )
            kv_cache.append(layer_cache)
        
        return kv_cache
    
    def swap_in(self, src_to_dst: Dict[int, int]) -> None:
        """从CPU交换KV Cache到GPU，支持量化"""
        for src_block_number, dst_block_number in src_to_dst.items():
            for layer_idx in range(len(self.gpu_cache)):
                src_block = self.cpu_cache[layer_idx][src_block_number]
                dst_block = self.gpu_cache[layer_idx][dst_block_number]
                
                # 如果数据类型不同，需要转换
                if src_block.dtype != dst_block.dtype:
                    # 处理量化数据类型转换
                    dst_block.copy_(src_block.to(dst_block.dtype))
                else:
                    dst_block.copy_(src_block)
```

## 实践任务

### 任务1: KV Cache量化基础实现

创建 `kv_cache_quantization_demo.py`:

```python
import torch
import torch.nn.functional as F

class SimpleKVCacheQuantizer:
    """简单的KV Cache量化器"""
    
    def __init__(self, dtype="fp8_e4m3"):
        self.dtype = dtype
        
    def quantize_fp8(self, tensor):
        """FP8量化"""
        # 计算动态缩放因子
        amax = torch.max(torch.abs(tensor))
        scale = amax / 448.0  # FP8 E4M3的最大值
        
        # 量化
        quantized = (tensor / scale).to(torch.float8_e4m3fn)
        return quantized, scale
    
    def dequantize_fp8(self, quantized_tensor, scale):
        """FP8反量化"""
        return quantized_tensor.to(torch.float16) * scale
    
    def quantize_int8(self, tensor):
        """INT8量化"""
        # 对称量化
        amax = torch.max(torch.abs(tensor))
        scale = amax / 127.0
        
        quantized = torch.round(tensor / scale)
        quantized = torch.clamp(quantized, -128, 127).to(torch.int8)
        return quantized, scale
    
    def dequantize_int8(self, quantized_tensor, scale):
        """INT8反量化"""
        return quantized_tensor.to(torch.float16) * scale

def test_kv_cache_quantization():
    """测试KV Cache量化"""
    # 模拟KV Cache张量
    batch_size, num_heads, seq_len, head_dim = 4, 32, 1024, 128
    
    # 生成随机Key和Value张量
    key = torch.randn(batch_size, num_heads, seq_len, head_dim, dtype=torch.float16)
    value = torch.randn(batch_size, num_heads, seq_len, head_dim, dtype=torch.float16)
    
    quantizer = SimpleKVCacheQuantizer()
    
    print("原始张量信息:")
    print(f"Key shape: {key.shape}, dtype: {key.dtype}")
    print(f"Value shape: {value.shape}, dtype: {value.dtype}")
    print(f"原始内存占用: {(key.numel() + value.numel()) * 2} bytes")
    
    # FP8量化测试
    print("\n=== FP8量化测试 ===")
    key_fp8, key_scale = quantizer.quantize_fp8(key)
    value_fp8, value_scale = quantizer.quantize_fp8(value)
    
    print(f"量化后内存占用: {(key_fp8.numel() + value_fp8.numel()) * 1} bytes")
    print(f"内存压缩比: {2.0:.1f}x")
    
    # 反量化
    key_dequant = quantizer.dequantize_fp8(key_fp8, key_scale)
    value_dequant = quantizer.dequantize_fp8(value_fp8, value_scale)
    
    # 计算量化误差
    key_error = torch.mean(torch.abs(key - key_dequant))
    value_error = torch.mean(torch.abs(value - value_dequant))
    
    print(f"Key量化误差: {key_error:.6f}")
    print(f"Value量化误差: {value_error:.6f}")
    
    # INT8量化测试
    print("\n=== INT8量化测试 ===")
    key_int8, key_scale_int8 = quantizer.quantize_int8(key)
    value_int8, value_scale_int8 = quantizer.quantize_int8(value)
    
    print(f"量化后内存占用: {(key_int8.numel() + value_int8.numel()) * 1} bytes")
    print(f"内存压缩比: {2.0:.1f}x")
    
    # 反量化
    key_dequant_int8 = quantizer.dequantize_int8(key_int8, key_scale_int8)
    value_dequant_int8 = quantizer.dequantize_int8(value_int8, value_scale_int8)
    
    # 计算量化误差
    key_error_int8 = torch.mean(torch.abs(key - key_dequant_int8))
    value_error_int8 = torch.mean(torch.abs(value - value_dequant_int8))
    
    print(f"Key量化误差: {key_error_int8:.6f}")
    print(f"Value量化误差: {value_error_int8:.6f}")

if __name__ == "__main__":
    test_kv_cache_quantization()
```

### 任务2: 注意力机制中的量化集成

创建 `attention_with_quantized_kv.py`:

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class QuantizedKVAttention(nn.Module):
    """集成KV Cache量化的注意力层"""
    
    def __init__(self, d_model, num_heads, kv_quantization="fp8"):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        self.scale = 1.0 / math.sqrt(self.head_dim)
        self.kv_quantization = kv_quantization
        
        # 线性投影层
        self.q_proj = nn.Linear(d_model, d_model, bias=False)
        self.k_proj = nn.Linear(d_model, d_model, bias=False)
        self.v_proj = nn.Linear(d_model, d_model, bias=False)
        self.o_proj = nn.Linear(d_model, d_model, bias=False)
        
        # KV Cache量化缩放因子
        self.register_buffer('k_scale', torch.tensor(1.0))
        self.register_buffer('v_scale', torch.tensor(1.0))
        
    def quantize_kv(self, k, v):
        """量化Key和Value"""
        if self.kv_quantization == "fp8":
            # FP8量化
            k_amax = torch.max(torch.abs(k))
            v_amax = torch.max(torch.abs(v))
            
            k_scale = k_amax / 448.0
            v_scale = v_amax / 448.0
            
            k_quant = (k / k_scale).to(torch.float8_e4m3fn)
            v_quant = (v / v_scale).to(torch.float8_e4m3fn)
            
            return k_quant, v_quant, k_scale, v_scale
            
        elif self.kv_quantization == "int8":
            # INT8量化
            k_amax = torch.max(torch.abs(k))
            v_amax = torch.max(torch.abs(v))
            
            k_scale = k_amax / 127.0
            v_scale = v_amax / 127.0
            
            k_quant = torch.round(k / k_scale).clamp(-128, 127).to(torch.int8)
            v_quant = torch.round(v / v_scale).clamp(-128, 127).to(torch.int8)
            
            return k_quant, v_quant, k_scale, v_scale
        else:
            return k, v, torch.tensor(1.0), torch.tensor(1.0)
    
    def dequantize_kv(self, k_quant, v_quant, k_scale, v_scale):
        """反量化Key和Value"""
        if self.kv_quantization in ["fp8", "int8"]:
            k = k_quant.to(torch.float16) * k_scale
            v = v_quant.to(torch.float16) * v_scale
            return k, v
        else:
            return k_quant, v_quant
    
    def forward(self, x, kv_cache=None, use_cache=False):
        """前向传播"""
        batch_size, seq_len, _ = x.shape
        
        # 计算Q, K, V
        q = self.q_proj(x)
        k = self.k_proj(x)
        v = self.v_proj(x)
        
        # 重塑为多头形状
        q = q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 如果使用KV Cache
        if use_cache:
            if kv_cache is not None:
                # 从缓存中获取之前的K, V
                cached_k, cached_v, k_scale, v_scale = kv_cache
                
                # 反量化缓存的K, V
                cached_k_dequant, cached_v_dequant = self.dequantize_kv(
                    cached_k, cached_v, k_scale, v_scale)
                
                # 拼接新的K, V
                k = torch.cat([cached_k_dequant, k], dim=2)
                v = torch.cat([cached_v_dequant, v], dim=2)
            
            # 量化当前的K, V用于缓存
            k_quant, v_quant, k_scale, v_scale = self.quantize_kv(k, v)
            new_kv_cache = (k_quant, v_quant, k_scale, v_scale)
        else:
            new_kv_cache = None
        
        # 计算注意力
        attn_weights = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        attn_weights = F.softmax(attn_weights, dim=-1)
        attn_output = torch.matmul(attn_weights, v)
        
        # 重塑输出
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model)
        
        # 输出投影
        output = self.o_proj(attn_output)
        
        if use_cache:
            return output, new_kv_cache
        else:
            return output

def test_quantized_attention():
    """测试量化注意力层"""
    d_model, num_heads = 512, 8
    batch_size, seq_len = 2, 100
    
    # 创建量化注意力层
    attn_fp8 = QuantizedKVAttention(d_model, num_heads, "fp8")
    attn_int8 = QuantizedKVAttention(d_model, num_heads, "int8")
    attn_none = QuantizedKVAttention(d_model, num_heads, "none")
    
    # 输入数据
    x = torch.randn(batch_size, seq_len, d_model, dtype=torch.float16)
    
    print("=== 量化注意力测试 ===")
    
    # 测试不同量化方法
    for name, attn_layer in [("FP8", attn_fp8), ("INT8", attn_int8), ("无量化", attn_none)]:
        print(f"\n{name}量化:")
        
        # 前向传播
        output, kv_cache = attn_layer(x, use_cache=True)
        
        print(f"输出形状: {output.shape}")
        if kv_cache:
            k_quant, v_quant, k_scale, v_scale = kv_cache
            print(f"缓存K形状: {k_quant.shape}, 数据类型: {k_quant.dtype}")
            print(f"缓存V形状: {v_quant.shape}, 数据类型: {v_quant.dtype}")
            print(f"K缩放因子: {k_scale:.6f}, V缩放因子: {v_scale:.6f}")

if __name__ == "__main__":
    test_quantized_attention()
```

## 学习检查点

### 第1天结束检查 (9.8)
- [ ] 理解KV Cache的基本原理和内存挑战
- [ ] 掌握BaseKVCacheMethod的设计思想
- [ ] 理解量化缩放因子的计算和存储
- [ ] 完成KV Cache量化基础实现

### 第2天结束检查 (9.9)
- [ ] 深入理解FP8 KV Cache量化实现
- [ ] 掌握注意力层中的量化集成方式
- [ ] 理解缓存引擎的量化支持机制
- [ ] 完成量化注意力层实现

## 参考资料

### vLLM核心源码文件
1. `vllm/model_executor/layers/quantization/kv_cache.py` - KV Cache量化基类
2. `vllm/worker/cache_engine.py` - 缓存引擎实现
3. `vllm/attention/layer.py` - 注意力层实现
4. `vllm/attention/backends/` - 注意力后端实现

### 技术论文
1. "KV-Cache Quantization for Large Language Models"
2. "FP8-LM: Training FP8 Large Language Models"
3. "Memory-Efficient Attention for Large Language Models"

## 下一步预告

完成KV Cache量化学习后，下一步将深入学习vLLM量化算子，重点关注：
- Quant/Dequant算子的CUDA实现
- LayerNorm融合算子的优化策略
- GEMM量化算子的性能优化
- Triton kernel的编写和调优
