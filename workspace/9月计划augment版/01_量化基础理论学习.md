# 量化基础理论学习文档

## 学习时间：9.1-9.2 (2天)

## 学习目标

1. 深入理解量化的数学原理和基本概念
2. 掌握INT8、FP8、INT4量化的技术细节
3. 理解不同量化策略的优缺点和适用场景
4. 为后续vLLM源码学习打下理论基础

## 理论基础

### 1. 量化的数学原理

#### 1.1 基本概念
量化是将连续的浮点数映射到离散整数空间的过程：

```
Q(x) = round(x / scale) + zero_point
```

其中：
- `x`: 原始浮点数
- `scale`: 量化缩放因子
- `zero_point`: 零点偏移
- `Q(x)`: 量化后的整数

#### 1.2 反量化过程
```
x_dequant = (Q(x) - zero_point) * scale
```

#### 1.3 量化误差分析
量化误差主要来源：
- **舍入误差**: round操作引入的误差
- **截断误差**: 超出量化范围的截断
- **累积误差**: 多次量化操作的误差累积

### 2. 量化分类详解

#### 2.1 按数据类型分类

##### INT8量化
- **范围**: [-128, 127] (有符号) 或 [0, 255] (无符号)
- **精度**: 8位整数
- **优势**: 硬件支持好，计算效率高
- **应用**: 权重和激活量化的主流选择

**vLLM中的INT8实现**:
```python
# 来源: vllm/model_executor/layers/quantization/utils/int8_utils.py
def quantize_int8(tensor, scale, zero_point=0):
    """INT8量化实现"""
    quantized = torch.round(tensor / scale) + zero_point
    return torch.clamp(quantized, -128, 127).to(torch.int8)
```

##### FP8量化
- **E4M3格式**: 4位指数，3位尾数
- **E5M2格式**: 5位指数，2位尾数
- **优势**: 保持浮点数的动态范围
- **应用**: 训练和推理的新兴选择

**vLLM中的FP8配置**:
```python
# 来源: vllm/model_executor/layers/quantization/fp8.py
class Fp8Config(QuantizationConfig):
    def __init__(self, 
                 activation_scheme: str = "dynamic",
                 weight_block_size: Optional[list[int]] = None):
        self.activation_scheme = activation_scheme  # "static" or "dynamic"
        self.weight_block_size = weight_block_size
```

##### INT4量化
- **范围**: [-8, 7] (有符号) 或 [0, 15] (无符号)
- **精度**: 4位整数
- **优势**: 极高的压缩比
- **挑战**: 精度损失较大，需要精细调优

#### 2.2 按量化策略分类

##### 对称量化 vs 非对称量化

**对称量化**:
```
Q(x) = round(x / scale)
```
- 零点固定为0
- 计算简单，硬件友好
- 适用于分布对称的数据

**非对称量化**:
```
Q(x) = round(x / scale) + zero_point
```
- 零点可调整
- 更好地利用量化范围
- 适用于分布不对称的数据

##### 静态量化 vs 动态量化

**静态量化**:
- 量化参数在推理前确定
- 需要校准数据集
- 推理时开销小

**动态量化**:
- 量化参数在推理时计算
- 无需校准数据
- 推理时有额外开销

**vLLM中的动态量化实现**:
```python
# 来源: vllm/model_executor/layers/quantization/fp8.py
def dynamic_quantize_fp8(tensor):
    """动态FP8量化"""
    amax = torch.max(torch.abs(tensor))
    scale = amax / torch.finfo(torch.float8_e4m3fn).max
    return tensor / scale, scale
```

##### Per-tensor vs Per-channel量化

**Per-tensor量化**:
- 整个张量使用同一组量化参数
- 计算简单，内存开销小
- 可能损失精度

**Per-channel量化**:
- 每个通道使用独立的量化参数
- 精度更高，但计算复杂
- 内存开销较大

### 3. vLLM量化架构分析

#### 3.1 量化配置基类
```python
# 来源: vllm/model_executor/layers/quantization/base_config.py
class QuantizationConfig:
    """量化配置基类"""
    
    def get_name(self) -> str:
        """返回量化方法名称"""
        raise NotImplementedError
    
    def get_supported_act_dtypes(self) -> Set[torch.dtype]:
        """返回支持的激活数据类型"""
        raise NotImplementedError
    
    def get_min_capability(self) -> int:
        """返回最小GPU计算能力要求"""
        return 60
```

#### 3.2 量化方法基类
```python
# 来源: vllm/model_executor/layers/quantization/base_config.py
class QuantizeMethodBase:
    """量化方法基类"""
    
    def create_weights(self, layer: torch.nn.Module):
        """创建量化权重"""
        raise NotImplementedError
    
    def apply(self, layer: torch.nn.Module) -> torch.Tensor:
        """应用量化方法"""
        raise NotImplementedError
```

#### 3.3 标量类型系统
```python
# 来源: vllm/scalar_type.py
class ScalarType:
    """标量类型定义"""
    
    def __init__(self, name: str, size_bits: int, 
                 is_signed: bool, is_floating_point: bool):
        self.name = name
        self.size_bits = size_bits
        self.is_signed = is_signed
        self.is_floating_point = is_floating_point

# 预定义的标量类型
scalar_types = {
    "int4": ScalarType("int4", 4, True, False),
    "uint4": ScalarType("uint4", 4, False, False),
    "int8": ScalarType("int8", 8, True, False),
    "fp8_e4m3": ScalarType("fp8_e4m3", 8, True, True),
    "fp8_e5m2": ScalarType("fp8_e5m2", 8, True, True),
}
```

## 实践任务

### 任务1: 实现基础量化函数

创建 `quantization_demo.py`:

```python
import torch
import numpy as np

def symmetric_quantize_int8(tensor):
    """对称INT8量化"""
    max_val = torch.max(torch.abs(tensor))
    scale = max_val / 127.0
    quantized = torch.round(tensor / scale)
    return torch.clamp(quantized, -128, 127).to(torch.int8), scale

def asymmetric_quantize_int8(tensor):
    """非对称INT8量化"""
    min_val = torch.min(tensor)
    max_val = torch.max(tensor)
    scale = (max_val - min_val) / 255.0
    zero_point = torch.round(-min_val / scale)
    quantized = torch.round(tensor / scale) + zero_point
    return torch.clamp(quantized, 0, 255).to(torch.uint8), scale, zero_point

def dequantize_int8(quantized, scale, zero_point=0):
    """INT8反量化"""
    return (quantized.float() - zero_point) * scale

# 测试代码
if __name__ == "__main__":
    # 创建测试张量
    x = torch.randn(1000) * 10
    
    # 对称量化测试
    q_sym, scale_sym = symmetric_quantize_int8(x)
    x_dequant_sym = dequantize_int8(q_sym, scale_sym)
    error_sym = torch.mean(torch.abs(x - x_dequant_sym))
    print(f"对称量化误差: {error_sym:.6f}")
    
    # 非对称量化测试
    q_asym, scale_asym, zp_asym = asymmetric_quantize_int8(x)
    x_dequant_asym = dequantize_int8(q_asym, scale_asym, zp_asym)
    error_asym = torch.mean(torch.abs(x - x_dequant_asym))
    print(f"非对称量化误差: {error_asym:.6f}")
```

### 任务2: 量化误差分析

创建 `error_analysis.py`:

```python
import torch
import matplotlib.pyplot as plt

def analyze_quantization_error(tensor, bits_list=[4, 8, 16]):
    """分析不同位宽的量化误差"""
    errors = []
    
    for bits in bits_list:
        max_int = 2**(bits-1) - 1
        min_int = -2**(bits-1)
        
        # 对称量化
        max_val = torch.max(torch.abs(tensor))
        scale = max_val / max_int
        quantized = torch.round(tensor / scale)
        quantized = torch.clamp(quantized, min_int, max_int)
        dequantized = quantized * scale
        
        error = torch.mean(torch.abs(tensor - dequantized))
        errors.append(error.item())
        
        print(f"{bits}位量化误差: {error:.6f}")
    
    return errors

# 测试不同分布的数据
distributions = {
    "正态分布": torch.randn(10000),
    "均匀分布": torch.rand(10000) * 20 - 10,
    "指数分布": torch.exponential(torch.ones(10000))
}

for name, data in distributions.items():
    print(f"\n{name}:")
    analyze_quantization_error(data)
```

### 任务3: vLLM量化配置实验

创建 `vllm_quant_config.py`:

```python
from vllm.model_executor.layers.quantization.fp8 import Fp8Config
from vllm.model_executor.layers.quantization.awq import AWQConfig
import torch

def test_fp8_config():
    """测试FP8量化配置"""
    # 动态激活量化配置
    config_dynamic = Fp8Config(
        activation_scheme="dynamic",
        is_checkpoint_fp8_serialized=False
    )
    
    # 静态激活量化配置
    config_static = Fp8Config(
        activation_scheme="static",
        is_checkpoint_fp8_serialized=True
    )
    
    print("FP8配置测试:")
    print(f"动态配置: {config_dynamic.activation_scheme}")
    print(f"静态配置: {config_static.activation_scheme}")

def test_awq_config():
    """测试AWQ量化配置"""
    config = AWQConfig(
        weight_bits=4,
        group_size=128,
        zero_point=True
    )
    
    print("AWQ配置测试:")
    print(f"权重位宽: {config.weight_bits}")
    print(f"分组大小: {config.group_size}")

if __name__ == "__main__":
    test_fp8_config()
    test_awq_config()
```

## 学习检查点

### 第1天结束检查
- [ ] 理解量化的基本数学原理
- [ ] 掌握对称/非对称量化的区别
- [ ] 完成基础量化函数实现
- [ ] 分析不同量化方法的误差特性

### 第2天结束检查
- [ ] 理解vLLM的量化架构设计
- [ ] 掌握不同量化配置的使用方法
- [ ] 完成量化误差分析实验
- [ ] 能够解释INT8/FP8/INT4的技术特点

## 参考资料

### 核心论文
1. "Quantization and Training of Neural Networks for Efficient Integer-Arithmetic-Only Inference"
2. "Integer Quantization for Deep Learning Inference: Principles and Empirical Evaluation"
3. "FP8 Formats for Deep Learning"

### vLLM源码文件
1. `vllm/model_executor/layers/quantization/base_config.py` - 量化配置基类
2. `vllm/scalar_type.py` - 标量类型定义
3. `vllm/model_executor/layers/quantization/schema.py` - 量化模式定义

### 在线资源
1. [PyTorch量化教程](https://pytorch.org/tutorials/advanced/static_quantization_tutorial.html)
2. [NVIDIA FP8技术白皮书](https://developer.nvidia.com/blog/fp8-formats-for-deep-learning/)
3. [量化技术综述](https://arxiv.org/abs/2103.13630)

## 下一步预告

完成量化基础理论学习后，下一步将深入学习vLLM框架的整体架构，重点关注：
- 模型执行器的设计模式
- 量化配置的集成机制
- 推理引擎的优化策略

这将为后续的KV Cache量化和算子优化学习奠定框架基础。
