# LLMCompressor与vLLM集成学习文档

## 学习时间：9.18-9.19 (2天)

## 学习目标

1. 深入理解LLMCompressor的量化流程和工作原理
2. 掌握模型格式转换和兼容性处理方法
3. 学习端到端的量化工作流程
4. 理解LLMCompressor与vLLM的集成机制
5. 掌握性能优化和部署策略

## LLMCompressor架构深度分析

### 1. LLMCompressor核心组件

#### 1.1 修改器(Modifier)架构

```python
# 来源: llmcompressor/modifiers/modifier.py
class Modifier:
    """修改器基类，定义模型修改的通用接口"""
    
    def __init__(self,
                 targets: Union[str, List[str]] = None,
                 start_epoch: float = -1,
                 end_epoch: float = -1,
                 update_frequency: float = -1.0,
                 min_frequency: float = 0.0,
                 end_comparator: Union[int, Callable[[int, int], bool]] = None,
                 enabled: bool = True,
                 **kwargs):
        
        self.targets = targets
        self.start_epoch = start_epoch
        self.end_epoch = end_epoch
        self.update_frequency = update_frequency
        self.min_frequency = min_frequency
        self.end_comparator = end_comparator
        self.enabled = enabled
    
    def initialize(self, module: torch.nn.Module, **kwargs):
        """初始化修改器"""
        raise NotImplementedError()
    
    def finalize(self, module: torch.nn.Module, **kwargs):
        """完成修改器应用"""
        raise NotImplementedError()
    
    def update(self, module: torch.nn.Module, optimizer, epoch: float, steps_per_epoch: int, **kwargs):
        """更新修改器状态"""
        raise NotImplementedError()

# 来源: llmcompressor/modifiers/quantization/quantization/base.py
class QuantizationModifier(Modifier):
    """量化修改器基类"""
    
    def __init__(self,
                 targets: Union[str, List[str]] = None,
                 scheme: Union[str, Dict] = None,
                 ignore: Union[str, List[str]] = None,
                 disable_quantization_observer_epoch: float = 2.0,
                 num_calibration_steps: int = 512,
                 **kwargs):
        
        super().__init__(targets=targets, **kwargs)
        self.scheme = scheme
        self.ignore = ignore
        self.disable_quantization_observer_epoch = disable_quantization_observer_epoch
        self.num_calibration_steps = num_calibration_steps
        
        # 量化配置解析
        self.quantization_scheme = self._parse_quantization_scheme(scheme)
    
    def _parse_quantization_scheme(self, scheme):
        """解析量化方案"""
        if isinstance(scheme, str):
            # 预定义方案
            if scheme == "W8A8":
                return {
                    "weights": {"num_bits": 8, "symmetric": True},
                    "input_activations": {"num_bits": 8, "symmetric": True}
                }
            elif scheme == "W4A16":
                return {
                    "weights": {"num_bits": 4, "symmetric": False, "group_size": 128},
                    "input_activations": {"num_bits": 16}
                }
        elif isinstance(scheme, dict):
            return scheme
        else:
            raise ValueError(f"Invalid quantization scheme: {scheme}")
    
    def initialize(self, module: torch.nn.Module, **kwargs):
        """初始化量化"""
        
        # 获取目标层
        target_modules = self._get_target_modules(module)
        
        # 应用量化方案
        for name, target_module in target_modules.items():
            self._apply_quantization_to_module(target_module, name)
    
    def _get_target_modules(self, module: torch.nn.Module) -> Dict[str, torch.nn.Module]:
        """获取目标模块"""
        target_modules = {}
        
        for name, submodule in module.named_modules():
            if self._should_quantize_module(name, submodule):
                target_modules[name] = submodule
        
        return target_modules
    
    def _should_quantize_module(self, name: str, module: torch.nn.Module) -> bool:
        """判断是否应该量化模块"""
        
        # 检查忽略列表
        if self.ignore and any(ignore_pattern in name for ignore_pattern in self.ignore):
            return False
        
        # 检查目标列表
        if self.targets:
            return any(target_pattern in name for target_pattern in self.targets)
        
        # 默认量化线性层和卷积层
        return isinstance(module, (torch.nn.Linear, torch.nn.Conv2d))
```

#### 1.2 GPTQ量化实现

```python
# 来源: llmcompressor/modifiers/quantization/gptq/gptq_quantize.py
class GPTQModifier(QuantizationModifier):
    """GPTQ量化修改器"""
    
    def __init__(self,
                 targets: Union[str, List[str]] = None,
                 scheme: Union[str, Dict] = None,
                 ignore: Union[str, List[str]] = None,
                 dampening_frac: float = 0.01,
                 block_size: int = 128,
                 quantize_activations: bool = False,
                 true_sequential: bool = False,
                 **kwargs):
        
        super().__init__(targets=targets, scheme=scheme, ignore=ignore, **kwargs)
        self.dampening_frac = dampening_frac
        self.block_size = block_size
        self.quantize_activations = quantize_activations
        self.true_sequential = true_sequential
        
        # GPTQ状态
        self.gptq_enabled = False
        self.gptq_handles = {}
    
    def initialize(self, module: torch.nn.Module, **kwargs):
        """初始化GPTQ量化"""
        
        super().initialize(module, **kwargs)
        
        # 为每个目标层创建GPTQ处理器
        target_modules = self._get_target_modules(module)
        
        for name, target_module in target_modules.items():
            if isinstance(target_module, torch.nn.Linear):
                gptq_handler = GPTQWrapper(
                    target_module,
                    name,
                    self.quantization_scheme,
                    self.dampening_frac,
                    self.block_size
                )
                self.gptq_handles[name] = gptq_handler
                
                # 替换原始模块
                self._replace_module(module, name, gptq_handler)
    
    def update(self, module: torch.nn.Module, optimizer, epoch: float, steps_per_epoch: int, **kwargs):
        """更新GPTQ状态"""
        
        # 在指定epoch启用GPTQ
        if not self.gptq_enabled and epoch >= self.start_epoch:
            self._enable_gptq()
            self.gptq_enabled = True
        
        # 在指定epoch完成GPTQ
        if self.gptq_enabled and epoch >= self.end_epoch:
            self._finalize_gptq()
    
    def _enable_gptq(self):
        """启用GPTQ量化"""
        for handler in self.gptq_handles.values():
            handler.enable_gptq()
    
    def _finalize_gptq(self):
        """完成GPTQ量化"""
        for handler in self.gptq_handles.values():
            handler.finalize_gptq()

class GPTQWrapper(torch.nn.Module):
    """GPTQ包装器"""
    
    def __init__(self,
                 layer: torch.nn.Linear,
                 name: str,
                 quantization_scheme: Dict,
                 dampening_frac: float = 0.01,
                 block_size: int = 128):
        
        super().__init__()
        self.layer = layer
        self.name = name
        self.quantization_scheme = quantization_scheme
        self.dampening_frac = dampening_frac
        self.block_size = block_size
        
        # GPTQ状态
        self.gptq_enabled = False
        self.quantized = False
        
        # 统计信息收集
        self.register_buffer('H', torch.zeros(layer.in_features, layer.in_features))
        self.register_buffer('nsamples', torch.zeros(1))
        
        # 量化参数
        self.weight_quantizer = None
        self.input_quantizer = None
        
        self._setup_quantizers()
    
    def _setup_quantizers(self):
        """设置量化器"""
        
        weight_config = self.quantization_scheme.get("weights", {})
        if weight_config:
            self.weight_quantizer = WeightQuantizer(
                num_bits=weight_config.get("num_bits", 8),
                symmetric=weight_config.get("symmetric", True),
                group_size=weight_config.get("group_size", -1)
            )
        
        activation_config = self.quantization_scheme.get("input_activations", {})
        if activation_config:
            self.input_quantizer = ActivationQuantizer(
                num_bits=activation_config.get("num_bits", 8),
                symmetric=activation_config.get("symmetric", True)
            )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        
        if self.gptq_enabled and not self.quantized:
            # 收集统计信息
            self._collect_input_stats(x)
        
        # 量化输入（如果需要）
        if self.input_quantizer and self.quantized:
            x = self.input_quantizer(x)
        
        # 执行线性变换
        if self.quantized and self.weight_quantizer:
            # 使用量化权重
            output = self._quantized_linear(x)
        else:
            # 使用原始权重
            output = F.linear(x, self.layer.weight, self.layer.bias)
        
        return output
    
    def _collect_input_stats(self, x: torch.Tensor):
        """收集输入统计信息用于GPTQ"""
        
        # 计算Hessian矩阵的近似
        # H = X^T @ X / n
        x_flat = x.view(-1, x.shape[-1])
        self.H += x_flat.t() @ x_flat
        self.nsamples += x_flat.shape[0]
    
    def enable_gptq(self):
        """启用GPTQ"""
        self.gptq_enabled = True
    
    def finalize_gptq(self):
        """完成GPTQ量化"""
        
        if not self.gptq_enabled:
            return
        
        # 计算平均Hessian
        H = self.H / self.nsamples
        
        # 执行GPTQ算法
        self._run_gptq_algorithm(H)
        
        # 标记为已量化
        self.quantized = True
        self.gptq_enabled = False
    
    def _run_gptq_algorithm(self, H: torch.Tensor):
        """运行GPTQ算法"""
        
        W = self.layer.weight.data.clone()
        
        # 添加阻尼
        damp = self.dampening_frac * torch.mean(torch.diag(H))
        diag = torch.arange(H.shape[0], device=H.device)
        H[diag, diag] += damp
        
        # Cholesky分解
        try:
            H_inv = torch.cholesky_inverse(torch.linalg.cholesky(H))
        except:
            # 如果Cholesky分解失败，使用伪逆
            H_inv = torch.pinverse(H)
        
        # 逐块量化
        for i in range(0, W.shape[1], self.block_size):
            end_idx = min(i + self.block_size, W.shape[1])
            
            # 量化当前块
            W_block = W[:, i:end_idx].clone()
            Q_block = self.weight_quantizer.quantize(W_block)
            
            # 计算量化误差
            E = W_block - Q_block
            
            # 更新后续权重
            if end_idx < W.shape[1]:
                H_inv_block = H_inv[i:end_idx, end_idx:]
                W[:, end_idx:] -= E @ H_inv_block
            
            # 更新量化权重
            W[:, i:end_idx] = Q_block
        
        # 保存量化权重
        self.layer.weight.data = W
```

### 2. 与vLLM的集成机制

#### 2.1 模型格式转换

```python
# LLMCompressor到vLLM的模型转换
class LLMCompressorToVLLMConverter:
    """LLMCompressor模型到vLLM格式的转换器"""
    
    def __init__(self):
        self.supported_quantizations = {
            "gptq": "gptq",
            "awq": "awq", 
            "smoothquant": "smoothquant",
            "fp8": "fp8"
        }
    
    def convert_model(self, 
                     llmcompressor_model_path: str,
                     output_path: str,
                     quantization_format: str = "auto") -> str:
        """转换LLMCompressor模型为vLLM兼容格式"""
        
        # 加载LLMCompressor模型
        model, tokenizer = self._load_llmcompressor_model(llmcompressor_model_path)
        
        # 检测量化格式
        if quantization_format == "auto":
            quantization_format = self._detect_quantization_format(model)
        
        # 转换配置
        vllm_config = self._convert_config(model.config, quantization_format)
        
        # 转换权重
        vllm_state_dict = self._convert_weights(model.state_dict(), quantization_format)
        
        # 保存vLLM兼容模型
        self._save_vllm_model(output_path, vllm_config, vllm_state_dict, tokenizer)
        
        return output_path
    
    def _detect_quantization_format(self, model) -> str:
        """检测量化格式"""
        
        # 检查模型配置
        if hasattr(model.config, 'quantization_config'):
            quant_config = model.config.quantization_config
            
            if 'gptq' in str(quant_config).lower():
                return "gptq"
            elif 'awq' in str(quant_config).lower():
                return "awq"
            elif 'smoothquant' in str(quant_config).lower():
                return "smoothquant"
        
        # 检查权重名称
        for name, param in model.named_parameters():
            if 'qweight' in name:
                return "gptq"
            elif 'weight_scale' in name:
                return "awq"
        
        return "none"
    
    def _convert_config(self, original_config, quantization_format: str) -> Dict:
        """转换模型配置"""
        
        vllm_config = {
            "model_type": original_config.model_type,
            "vocab_size": original_config.vocab_size,
            "hidden_size": original_config.hidden_size,
            "num_attention_heads": original_config.num_attention_heads,
            "num_hidden_layers": original_config.num_hidden_layers,
            "intermediate_size": getattr(original_config, 'intermediate_size', None),
            "max_position_embeddings": getattr(original_config, 'max_position_embeddings', 2048),
            "torch_dtype": "float16",
        }
        
        # 添加量化配置
        if quantization_format != "none":
            vllm_config["quantization_config"] = self._create_vllm_quant_config(
                original_config, quantization_format)
        
        return vllm_config
    
    def _create_vllm_quant_config(self, original_config, quantization_format: str) -> Dict:
        """创建vLLM量化配置"""
        
        if quantization_format == "gptq":
            return {
                "quant_method": "gptq",
                "bits": getattr(original_config.quantization_config, 'bits', 4),
                "group_size": getattr(original_config.quantization_config, 'group_size', 128),
                "desc_act": getattr(original_config.quantization_config, 'desc_act', False),
            }
        elif quantization_format == "awq":
            return {
                "quant_method": "awq",
                "weight_bits": 4,
                "group_size": 128,
                "zero_point": True,
            }
        elif quantization_format == "fp8":
            return {
                "quant_method": "fp8",
                "activation_scheme": "dynamic",
            }
        
        return {}
    
    def _convert_weights(self, state_dict: Dict, quantization_format: str) -> Dict:
        """转换权重格式"""
        
        vllm_state_dict = {}
        
        for name, tensor in state_dict.items():
            # 转换权重名称以匹配vLLM期望的格式
            vllm_name = self._convert_weight_name(name, quantization_format)
            
            # 转换权重数据
            vllm_tensor = self._convert_weight_data(tensor, name, quantization_format)
            
            vllm_state_dict[vllm_name] = vllm_tensor
        
        return vllm_state_dict
    
    def _convert_weight_name(self, name: str, quantization_format: str) -> str:
        """转换权重名称"""
        
        # LLMCompressor到vLLM的名称映射
        name_mappings = {
            "model.embed_tokens.weight": "model.embed_tokens.weight",
            "model.norm.weight": "model.norm.weight",
            "lm_head.weight": "lm_head.weight",
        }
        
        # 处理层权重
        if "layers." in name:
            # 提取层索引
            parts = name.split(".")
            layer_idx = parts[2]
            
            # 转换注意力权重
            if "self_attn" in name:
                if "q_proj" in name:
                    suffix = name.split("q_proj.")[-1]
                    return f"model.layers.{layer_idx}.self_attn.q_proj.{suffix}"
                elif "k_proj" in name:
                    suffix = name.split("k_proj.")[-1]
                    return f"model.layers.{layer_idx}.self_attn.k_proj.{suffix}"
                elif "v_proj" in name:
                    suffix = name.split("v_proj.")[-1]
                    return f"model.layers.{layer_idx}.self_attn.v_proj.{suffix}"
                elif "o_proj" in name:
                    suffix = name.split("o_proj.")[-1]
                    return f"model.layers.{layer_idx}.self_attn.o_proj.{suffix}"
            
            # 转换MLP权重
            elif "mlp" in name:
                if "gate_proj" in name:
                    suffix = name.split("gate_proj.")[-1]
                    return f"model.layers.{layer_idx}.mlp.gate_proj.{suffix}"
                elif "up_proj" in name:
                    suffix = name.split("up_proj.")[-1]
                    return f"model.layers.{layer_idx}.mlp.up_proj.{suffix}"
                elif "down_proj" in name:
                    suffix = name.split("down_proj.")[-1]
                    return f"model.layers.{layer_idx}.mlp.down_proj.{suffix}"
        
        return name_mappings.get(name, name)
    
    def _convert_weight_data(self, tensor: torch.Tensor, name: str, quantization_format: str) -> torch.Tensor:
        """转换权重数据"""
        
        # 根据量化格式进行特定转换
        if quantization_format == "gptq":
            # GPTQ权重可能需要重新打包
            if "qweight" in name:
                return self._repack_gptq_weights(tensor)
        elif quantization_format == "awq":
            # AWQ权重转换
            if "qweight" in name:
                return self._repack_awq_weights(tensor)
        
        # 默认情况下直接返回
        return tensor
    
    def _save_vllm_model(self, output_path: str, config: Dict, state_dict: Dict, tokenizer):
        """保存vLLM兼容模型"""
        
        import os
        import json
        
        os.makedirs(output_path, exist_ok=True)
        
        # 保存配置
        with open(os.path.join(output_path, "config.json"), "w") as f:
            json.dump(config, f, indent=2)
        
        # 保存权重
        torch.save(state_dict, os.path.join(output_path, "pytorch_model.bin"))
        
        # 保存tokenizer
        tokenizer.save_pretrained(output_path)
        
        print(f"vLLM兼容模型已保存到: {output_path}")
```

### 3. 端到端量化工作流

#### 3.1 完整的量化流程

```python
# 端到端量化工作流
class EndToEndQuantizationPipeline:
    """端到端量化流水线"""
    
    def __init__(self, 
                 model_name: str,
                 quantization_method: str = "gptq",
                 output_dir: str = "./quantized_model"):
        
        self.model_name = model_name
        self.quantization_method = quantization_method
        self.output_dir = output_dir
        
        # 支持的量化方法
        self.supported_methods = ["gptq", "awq", "smoothquant", "fp8"]
        
        if quantization_method not in self.supported_methods:
            raise ValueError(f"Unsupported quantization method: {quantization_method}")
    
    def run_quantization(self, 
                        calibration_dataset: str = "wikitext",
                        num_calibration_samples: int = 512,
                        **kwargs) -> str:
        """运行完整的量化流程"""
        
        print(f"开始量化流程: {self.model_name} -> {self.quantization_method}")
        
        # 步骤1: 加载原始模型
        print("1. 加载原始模型...")
        model, tokenizer = self._load_model()
        
        # 步骤2: 准备校准数据
        print("2. 准备校准数据...")
        calibration_data = self._prepare_calibration_data(
            tokenizer, calibration_dataset, num_calibration_samples)
        
        # 步骤3: 应用量化
        print("3. 应用量化...")
        quantized_model = self._apply_quantization(model, calibration_data, **kwargs)
        
        # 步骤4: 验证量化模型
        print("4. 验证量化模型...")
        self._validate_quantized_model(quantized_model, tokenizer)
        
        # 步骤5: 保存量化模型
        print("5. 保存量化模型...")
        self._save_quantized_model(quantized_model, tokenizer)
        
        # 步骤6: 转换为vLLM格式
        print("6. 转换为vLLM格式...")
        vllm_model_path = self._convert_to_vllm_format()
        
        print(f"量化完成! vLLM模型路径: {vllm_model_path}")
        return vllm_model_path
    
    def _apply_quantization(self, model, calibration_data, **kwargs):
        """应用量化方法"""
        
        if self.quantization_method == "gptq":
            return self._apply_gptq(model, calibration_data, **kwargs)
        elif self.quantization_method == "awq":
            return self._apply_awq(model, calibration_data, **kwargs)
        elif self.quantization_method == "smoothquant":
            return self._apply_smoothquant(model, calibration_data, **kwargs)
        elif self.quantization_method == "fp8":
            return self._apply_fp8(model, calibration_data, **kwargs)
    
    def _apply_gptq(self, model, calibration_data, **kwargs):
        """应用GPTQ量化"""
        
        from llmcompressor.transformers import oneshot
        from llmcompressor.modifiers.quantization import GPTQModifier
        
        # 创建GPTQ配置
        recipe = {
            "modifiers": [
                {
                    "GPTQModifier": {
                        "targets": ["Linear"],
                        "scheme": {
                            "weights": {
                                "num_bits": kwargs.get("bits", 4),
                                "symmetric": False,
                                "group_size": kwargs.get("group_size", 128)
                            }
                        },
                        "dampening_frac": kwargs.get("dampening_frac", 0.01),
                        "block_size": kwargs.get("block_size", 128)
                    }
                }
            ]
        }
        
        # 执行量化
        oneshot(
            model=model,
            dataset=calibration_data,
            recipe=recipe,
            output_dir=self.output_dir,
            num_calibration_samples=len(calibration_data)
        )
        
        return model
    
    def _apply_awq(self, model, calibration_data, **kwargs):
        """应用AWQ量化"""
        
        from llmcompressor.transformers import oneshot
        from llmcompressor.modifiers.quantization import AWQModifier
        
        recipe = {
            "modifiers": [
                {
                    "AWQModifier": {
                        "targets": ["Linear"],
                        "scheme": {
                            "weights": {
                                "num_bits": 4,
                                "symmetric": False,
                                "group_size": 128
                            }
                        }
                    }
                }
            ]
        }
        
        oneshot(
            model=model,
            dataset=calibration_data,
            recipe=recipe,
            output_dir=self.output_dir,
            num_calibration_samples=len(calibration_data)
        )
        
        return model
```

## 实践任务

### 任务1: LLMCompressor基础使用

创建 `llmcompressor_basic_usage.py`:

```python
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from llmcompressor.transformers import oneshot
from llmcompressor.modifiers.quantization import GPTQModifier
import os

def basic_gptq_quantization():
    """基础GPTQ量化示例"""
    
    print("=== LLMCompressor GPTQ量化示例 ===")
    
    # 模型配置
    model_name = "microsoft/DialoGPT-small"  # 使用小模型进行测试
    output_dir = "./quantized_model_gptq"
    
    # 加载模型和tokenizer
    print("1. 加载模型...")
    model = AutoModelForCausalLM.from_pretrained(
        model_name, 
        torch_dtype=torch.float16,
        device_map="auto"
    )
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    # 准备校准数据
    print("2. 准备校准数据...")
    calibration_texts = [
        "The future of artificial intelligence is",
        "Machine learning has revolutionized",
        "Deep learning models are becoming",
        "Natural language processing enables",
        "Computer vision applications include"
    ]
    
    def tokenize_function(examples):
        return tokenizer(examples, truncation=True, padding=True, max_length=512)
    
    calibration_data = [tokenize_function(text) for text in calibration_texts]
    
    # 创建GPTQ配置
    print("3. 配置GPTQ量化...")
    recipe = {
        "modifiers": [
            {
                "GPTQModifier": {
                    "targets": ["Linear"],
                    "scheme": {
                        "weights": {
                            "num_bits": 4,
                            "symmetric": False,
                            "group_size": 128
                        }
                    },
                    "dampening_frac": 0.01,
                    "block_size": 128
                }
            }
        ]
    }
    
    # 执行量化
    print("4. 执行量化...")
    try:
        oneshot(
            model=model,
            dataset=calibration_data,
            recipe=recipe,
            output_dir=output_dir,
            num_calibration_samples=len(calibration_data),
            save_compressed=True
        )
        print(f"量化完成，模型保存在: {output_dir}")
        
        # 验证量化模型
        print("5. 验证量化模型...")
        quantized_model = AutoModelForCausalLM.from_pretrained(output_dir)
        
        # 简单推理测试
        test_input = tokenizer("Hello, how are you?", return_tensors="pt")
        with torch.no_grad():
            output = quantized_model.generate(
                test_input.input_ids,
                max_length=50,
                num_return_sequences=1,
                temperature=0.7
            )
        
        generated_text = tokenizer.decode(output[0], skip_special_tokens=True)
        print(f"生成文本: {generated_text}")
        
    except Exception as e:
        print(f"量化过程出错: {e}")

def compare_model_sizes():
    """比较量化前后的模型大小"""
    
    original_model_path = "microsoft/DialoGPT-small"
    quantized_model_path = "./quantized_model_gptq"
    
    if not os.path.exists(quantized_model_path):
        print("请先运行量化流程")
        return
    
    # 加载模型
    original_model = AutoModelForCausalLM.from_pretrained(original_model_path)
    quantized_model = AutoModelForCausalLM.from_pretrained(quantized_model_path)
    
    # 计算参数数量
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters())
    
    original_params = count_parameters(original_model)
    quantized_params = count_parameters(quantized_model)
    
    print(f"\n=== 模型大小对比 ===")
    print(f"原始模型参数数量: {original_params:,}")
    print(f"量化模型参数数量: {quantized_params:,}")
    print(f"参数压缩比: {original_params / quantized_params:.2f}x")
    
    # 估算内存使用
    def estimate_memory_usage(model):
        total_memory = 0
        for param in model.parameters():
            total_memory += param.numel() * param.element_size()
        return total_memory / 1024 / 1024  # MB
    
    original_memory = estimate_memory_usage(original_model)
    quantized_memory = estimate_memory_usage(quantized_model)
    
    print(f"原始模型内存: {original_memory:.2f} MB")
    print(f"量化模型内存: {quantized_memory:.2f} MB")
    print(f"内存压缩比: {original_memory / quantized_memory:.2f}x")

if __name__ == "__main__":
    basic_gptq_quantization()
    compare_model_sizes()
```

### 任务2: 模型格式转换

创建 `model_format_conversion.py`:

```python
import torch
import json
import os
from transformers import AutoConfig, AutoTokenizer
from typing import Dict, Any

class ModelFormatConverter:
    """模型格式转换器"""
    
    def __init__(self):
        self.conversion_mappings = {
            "llmcompressor_to_vllm": self._convert_llmcompressor_to_vllm,
            "vllm_to_llmcompressor": self._convert_vllm_to_llmcompressor
        }
    
    def convert(self, 
                source_path: str, 
                target_path: str, 
                conversion_type: str,
                quantization_format: str = "auto") -> bool:
        """执行模型格式转换"""
        
        if conversion_type not in self.conversion_mappings:
            raise ValueError(f"Unsupported conversion type: {conversion_type}")
        
        print(f"开始转换: {source_path} -> {target_path}")
        print(f"转换类型: {conversion_type}")
        
        try:
            success = self.conversion_mappings[conversion_type](
                source_path, target_path, quantization_format)
            
            if success:
                print(f"转换成功: {target_path}")
            else:
                print("转换失败")
            
            return success
            
        except Exception as e:
            print(f"转换过程出错: {e}")
            return False
    
    def _convert_llmcompressor_to_vllm(self, 
                                      source_path: str, 
                                      target_path: str, 
                                      quantization_format: str) -> bool:
        """LLMCompressor到vLLM格式转换"""
        
        # 加载源模型配置
        source_config = AutoConfig.from_pretrained(source_path)
        tokenizer = AutoTokenizer.from_pretrained(source_path)
        
        # 检测量化格式
        if quantization_format == "auto":
            quantization_format = self._detect_quantization_format(source_path)
        
        print(f"检测到量化格式: {quantization_format}")
        
        # 创建vLLM兼容配置
        vllm_config = self._create_vllm_config(source_config, quantization_format)
        
        # 转换权重
        success = self._convert_weights_to_vllm(source_path, target_path, quantization_format)
        
        if success:
            # 保存vLLM配置
            os.makedirs(target_path, exist_ok=True)
            
            with open(os.path.join(target_path, "config.json"), "w") as f:
                json.dump(vllm_config, f, indent=2)
            
            # 保存tokenizer
            tokenizer.save_pretrained(target_path)
        
        return success
    
    def _detect_quantization_format(self, model_path: str) -> str:
        """检测量化格式"""
        
        config_path = os.path.join(model_path, "config.json")
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
            
            # 检查量化配置
            if "quantization_config" in config:
                quant_config = config["quantization_config"]
                
                if "gptq" in str(quant_config).lower():
                    return "gptq"
                elif "awq" in str(quant_config).lower():
                    return "awq"
        
        # 检查权重文件
        model_files = os.listdir(model_path)
        for file_name in model_files:
            if file_name.endswith((".bin", ".safetensors")):
                # 简单检查文件名中的关键词
                if "gptq" in file_name.lower():
                    return "gptq"
                elif "awq" in file_name.lower():
                    return "awq"
        
        return "none"
    
    def _create_vllm_config(self, source_config: AutoConfig, quantization_format: str) -> Dict[str, Any]:
        """创建vLLM兼容配置"""
        
        vllm_config = {
            "model_type": source_config.model_type,
            "vocab_size": source_config.vocab_size,
            "hidden_size": source_config.hidden_size,
            "num_attention_heads": source_config.num_attention_heads,
            "num_hidden_layers": source_config.num_hidden_layers,
            "intermediate_size": getattr(source_config, 'intermediate_size', None),
            "max_position_embeddings": getattr(source_config, 'max_position_embeddings', 2048),
            "torch_dtype": "float16",
            "transformers_version": "4.36.0",
        }
        
        # 添加量化配置
        if quantization_format == "gptq":
            vllm_config["quantization_config"] = {
                "quant_method": "gptq",
                "bits": 4,
                "group_size": 128,
                "desc_act": False,
                "static_groups": False,
                "sym": False,
                "lm_head": False,
                "damp_percent": 0.01,
                "true_sequential": True,
            }
        elif quantization_format == "awq":
            vllm_config["quantization_config"] = {
                "quant_method": "awq",
                "weight_bits": 4,
                "group_size": 128,
                "zero_point": True,
                "version": "GEMM",
            }
        
        return vllm_config
    
    def _convert_weights_to_vllm(self, 
                                source_path: str, 
                                target_path: str, 
                                quantization_format: str) -> bool:
        """转换权重到vLLM格式"""
        
        try:
            # 加载源权重
            source_weights = {}
            
            # 查找权重文件
            for file_name in os.listdir(source_path):
                if file_name.endswith(".bin"):
                    file_path = os.path.join(source_path, file_name)
                    weights = torch.load(file_path, map_location="cpu")
                    source_weights.update(weights)
            
            # 转换权重名称和格式
            vllm_weights = self._transform_weight_names(source_weights, quantization_format)
            
            # 保存vLLM权重
            os.makedirs(target_path, exist_ok=True)
            torch.save(vllm_weights, os.path.join(target_path, "pytorch_model.bin"))
            
            return True
            
        except Exception as e:
            print(f"权重转换失败: {e}")
            return False
    
    def _transform_weight_names(self, 
                               source_weights: Dict[str, torch.Tensor], 
                               quantization_format: str) -> Dict[str, torch.Tensor]:
        """转换权重名称"""
        
        vllm_weights = {}
        
        for name, tensor in source_weights.items():
            # 基本名称转换
            vllm_name = name
            
            # 特定量化格式的转换
            if quantization_format == "gptq":
                # GPTQ特定的权重名称转换
                if "qweight" in name:
                    vllm_name = name  # 保持原名
                elif "qzeros" in name:
                    vllm_name = name  # 保持原名
                elif "scales" in name:
                    vllm_name = name  # 保持原名
            
            vllm_weights[vllm_name] = tensor
        
        return vllm_weights

def test_model_conversion():
    """测试模型转换"""
    
    print("=== 模型格式转换测试 ===")
    
    # 假设已有量化模型
    source_path = "./quantized_model_gptq"
    target_path = "./vllm_compatible_model"
    
    if not os.path.exists(source_path):
        print("请先运行量化流程生成源模型")
        return
    
    # 创建转换器
    converter = ModelFormatConverter()
    
    # 执行转换
    success = converter.convert(
        source_path=source_path,
        target_path=target_path,
        conversion_type="llmcompressor_to_vllm",
        quantization_format="gptq"
    )
    
    if success:
        print("转换成功!")
        
        # 验证转换结果
        print("验证转换结果...")
        
        # 检查文件结构
        required_files = ["config.json", "pytorch_model.bin", "tokenizer.json"]
        for file_name in required_files:
            file_path = os.path.join(target_path, file_name)
            if os.path.exists(file_path):
                print(f"✓ {file_name} 存在")
            else:
                print(f"✗ {file_name} 缺失")
        
        # 检查配置
        config_path = os.path.join(target_path, "config.json")
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
            
            print(f"模型类型: {config.get('model_type', 'Unknown')}")
            print(f"量化方法: {config.get('quantization_config', {}).get('quant_method', 'None')}")
    else:
        print("转换失败!")

if __name__ == "__main__":
    test_model_conversion()
```

## 学习检查点

### 第1天结束检查 (9.18)
- [ ] 理解LLMCompressor的修改器架构
- [ ] 掌握GPTQ量化的实现细节
- [ ] 学习模型格式转换方法
- [ ] 完成LLMCompressor基础使用

### 第2天结束检查 (9.19)
- [ ] 掌握端到端量化工作流程
- [ ] 理解与vLLM的集成机制
- [ ] 完成模型格式转换实现
- [ ] 能够独立完成量化和部署流程

## 参考资料

### LLMCompressor核心源码文件
1. `llmcompressor/modifiers/` - 修改器实现
2. `llmcompressor/transformers/compression/` - 压缩工具
3. `llmcompressor/entrypoints/` - 入口点实现

### 技术文档
1. [LLMCompressor官方文档](https://github.com/vllm-project/llm-compressor)
2. [vLLM量化支持文档](https://docs.vllm.ai/en/latest/quantization/supported_hardware.html)

## 下一步预告

完成LLMCompressor与vLLM集成学习后，下一步将学习MoE量化技术，重点关注：
- MoE架构的特点和挑战
- 专家网络的量化策略
- 路由机制的优化
- vLLM中的MoE量化实现
