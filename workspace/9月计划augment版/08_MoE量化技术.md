# MoE量化技术学习文档

## 学习时间：9.22-9.23 (2天)

## 学习目标

1. 深入理解MoE (Mixture of Experts) 架构的特点和挑战
2. 掌握专家网络的量化策略和技术细节
3. 学习路由机制的优化和量化影响
4. 理解vLLM中MoE量化的实现方式
5. 掌握MoE模型的部署和推理优化

## MoE架构基础

### 1. MoE基本原理

#### 1.1 MoE架构概述

MoE通过条件计算实现模型容量的扩展，同时保持计算效率：

```
传统FFN: x -> Linear1 -> Activation -> Linear2 -> output
MoE FFN:  x -> Router -> Expert Selection -> Expert Computation -> output

其中：
- Router: 决定激活哪些专家
- Experts: 多个独立的FFN网络
- Top-K Selection: 只激活K个最相关的专家
```

#### 1.2 MoE的数学表示

```python
# MoE层的数学表示
def moe_forward(x, experts, router):
    """
    x: 输入张量 [batch_size, seq_len, hidden_size]
    experts: 专家网络列表 [num_experts]
    router: 路由网络
    """
    
    # 1. 路由计算
    router_logits = router(x)  # [batch_size, seq_len, num_experts]
    router_probs = softmax(router_logits, dim=-1)
    
    # 2. Top-K选择
    top_k_probs, top_k_indices = torch.topk(router_probs, k=2, dim=-1)
    top_k_probs = top_k_probs / torch.sum(top_k_probs, dim=-1, keepdim=True)  # 重新归一化
    
    # 3. 专家计算
    output = torch.zeros_like(x)
    for i in range(k):
        expert_idx = top_k_indices[:, :, i]
        expert_weight = top_k_probs[:, :, i]
        
        # 批量处理相同专家的tokens
        for expert_id in range(num_experts):
            mask = (expert_idx == expert_id)
            if mask.any():
                expert_input = x[mask]
                expert_output = experts[expert_id](expert_input)
                output[mask] += expert_weight[mask].unsqueeze(-1) * expert_output
    
    return output
```

### 2. vLLM中的MoE实现

#### 2.1 FusedMoE核心实现

```python
# 来源: vllm/model_executor/layers/fused_moe/fused_moe.py
class FusedMoE(torch.nn.Module):
    """融合的MoE实现"""
    
    def __init__(self, 
                 num_experts: int,
                 top_k: int,
                 hidden_size: int,
                 intermediate_size: int,
                 params_dtype: torch.dtype = torch.float16,
                 reduce_results: bool = False,
                 renormalize: bool = True,
                 use_grouped_topk: bool = False,
                 num_expert_group: Optional[int] = None,
                 topk_group: Optional[int] = None,
                 custom_routing_function: Optional[Callable] = None,
                 tp_size: int = 1):
        
        super().__init__()
        self.num_experts = num_experts
        self.top_k = top_k
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        self.reduce_results = reduce_results
        self.renormalize = renormalize
        self.use_grouped_topk = use_grouped_topk
        self.tp_size = tp_size
        
        # 专家权重 - 所有专家的权重打包在一起
        self.w13_weight = nn.Parameter(
            torch.empty(num_experts, 2 * intermediate_size, hidden_size, dtype=params_dtype)
        )
        self.w2_weight = nn.Parameter(
            torch.empty(num_experts, hidden_size, intermediate_size, dtype=params_dtype)
        )
        
        # 路由函数
        if custom_routing_function is None:
            self.routing_function = self.default_routing_function
        else:
            self.routing_function = custom_routing_function
    
    def default_routing_function(self, 
                                hidden_states: torch.Tensor,
                                routing_weights: torch.Tensor,
                                top_k: int,
                                renormalize: bool) -> Tuple[torch.Tensor, torch.Tensor]:
        """默认路由函数"""
        
        # 计算top-k专家
        routing_weights = F.softmax(routing_weights, dim=1, dtype=torch.float)
        topk_weights, topk_ids = torch.topk(routing_weights, top_k, dim=-1)
        
        # 重新归一化（如果需要）
        if renormalize:
            topk_weights = topk_weights / topk_weights.sum(dim=-1, keepdim=True)
        
        return topk_weights, topk_ids
    
    def forward(self,
                hidden_states: torch.Tensor,
                router_logits: torch.Tensor,
                top_k: Optional[int] = None) -> torch.Tensor:
        """MoE前向传播"""
        
        # 使用传入的top_k或默认值
        if top_k is None:
            top_k = self.top_k
        
        # 路由计算
        topk_weights, topk_ids = self.routing_function(
            hidden_states, router_logits, top_k, self.renormalize)
        
        # 调用融合MoE kernel
        return self.apply_moe(hidden_states, topk_weights, topk_ids)
    
    def apply_moe(self,
                  hidden_states: torch.Tensor,
                  topk_weights: torch.Tensor,
                  topk_ids: torch.Tensor) -> torch.Tensor:
        """应用MoE计算"""
        
        # 调用CUDA kernel进行高效计算
        return torch.ops._moe_C.fused_moe(
            hidden_states,
            self.w13_weight,
            self.w2_weight,
            topk_weights,
            topk_ids,
            self.renormalize,
            self.reduce_results,
            self.use_grouped_topk
        )
```

#### 2.2 MoE量化支持

```python
# 来源: vllm/model_executor/layers/quantization/experts_int8.py
class ExpertsInt8Config(QuantizationConfig):
    """专家网络INT8量化配置"""
    
    def __init__(self):
        self.quant_method = "experts_int8"
    
    def get_name(self) -> str:
        return "experts_int8"
    
    def get_supported_act_dtypes(self) -> Set[torch.dtype]:
        return {torch.float16, torch.bfloat16}
    
    def get_min_capability(self) -> int:
        return 75  # 需要较新的GPU架构
    
    def get_quant_method(self, layer: torch.nn.Module, prefix: str) -> Optional["QuantizeMethodBase"]:
        """获取量化方法"""
        if isinstance(layer, FusedMoE):
            return ExpertsInt8Method(self)
        return None

class ExpertsInt8Method(QuantizeMethodBase):
    """专家网络INT8量化方法"""
    
    def __init__(self, quant_config: ExpertsInt8Config):
        self.quant_config = quant_config
    
    def create_weights(self,
                      layer: torch.nn.Module,
                      num_experts: int,
                      hidden_size: int,
                      intermediate_size: int,
                      params_dtype: torch.dtype,
                      **kwargs):
        """创建量化权重"""
        
        # 量化的专家权重 (INT8)
        w13_weight_packed = ModelWeightParameter(
            data=torch.empty(
                num_experts,
                2 * intermediate_size,
                hidden_size // 2,  # INT8打包，每个元素存储2个INT8值
                dtype=torch.int8,
            ),
            input_dim=2,
            output_dim=1,
            packed_dim=2,
            packed_factor=2,
        )
        
        w2_weight_packed = ModelWeightParameter(
            data=torch.empty(
                num_experts,
                hidden_size,
                intermediate_size // 2,  # INT8打包
                dtype=torch.int8,
            ),
            input_dim=2,
            output_dim=1,
            packed_dim=2,
            packed_factor=2,
        )
        
        # 量化缩放因子
        w13_scale = ModelWeightParameter(
            data=torch.empty(num_experts, 2 * intermediate_size, dtype=params_dtype),
            expert_dim=0,
            output_dim=1,
        )
        
        w2_scale = ModelWeightParameter(
            data=torch.empty(num_experts, hidden_size, dtype=params_dtype),
            expert_dim=0,
            output_dim=1,
        )
        
        layer.register_parameter("w13_weight_packed", w13_weight_packed)
        layer.register_parameter("w2_weight_packed", w2_weight_packed)
        layer.register_parameter("w13_scale", w13_scale)
        layer.register_parameter("w2_scale", w2_scale)
    
    def apply(self,
              layer: torch.nn.Module,
              hidden_states: torch.Tensor,
              router_logits: torch.Tensor,
              top_k: int) -> torch.Tensor:
        """应用量化MoE"""
        
        # 路由计算
        topk_weights, topk_ids = layer.routing_function(
            hidden_states, router_logits, top_k, layer.renormalize)
        
        # 调用量化MoE kernel
        return torch.ops._moe_C.fused_moe_int8(
            hidden_states,
            layer.w13_weight_packed,
            layer.w2_weight_packed,
            layer.w13_scale,
            layer.w2_scale,
            topk_weights,
            topk_ids,
            layer.renormalize
        )
```

### 3. MoE量化策略

#### 3.1 专家级量化

```python
class ExpertWiseQuantization:
    """专家级量化策略"""
    
    def __init__(self, quantization_bits: int = 8):
        self.quantization_bits = quantization_bits
        self.max_int = 2**(quantization_bits - 1) - 1
        self.min_int = -2**(quantization_bits - 1)
    
    def quantize_experts_individually(self, expert_weights: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        对每个专家独立量化
        
        Args:
            expert_weights: [num_experts, output_dim, input_dim]
        
        Returns:
            quantized_weights: 量化后的权重
            scales: 每个专家的缩放因子
        """
        
        num_experts = expert_weights.shape[0]
        quantized_weights = torch.zeros_like(expert_weights, dtype=torch.int8)
        scales = torch.zeros(num_experts, dtype=expert_weights.dtype)
        
        for expert_idx in range(num_experts):
            expert_weight = expert_weights[expert_idx]
            
            # 计算该专家的缩放因子
            weight_max = torch.max(torch.abs(expert_weight))
            scale = weight_max / self.max_int
            scales[expert_idx] = scale
            
            # 量化该专家的权重
            quantized_weight = torch.round(expert_weight / scale).clamp(self.min_int, self.max_int)
            quantized_weights[expert_idx] = quantized_weight.to(torch.int8)
        
        return quantized_weights, scales
    
    def quantize_experts_grouped(self, 
                                expert_weights: torch.Tensor, 
                                group_size: int = 4) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        分组量化专家
        
        Args:
            expert_weights: [num_experts, output_dim, input_dim]
            group_size: 每组专家数量
        """
        
        num_experts = expert_weights.shape[0]
        quantized_weights = torch.zeros_like(expert_weights, dtype=torch.int8)
        scales = torch.zeros(num_experts, dtype=expert_weights.dtype)
        
        for group_start in range(0, num_experts, group_size):
            group_end = min(group_start + group_size, num_experts)
            group_weights = expert_weights[group_start:group_end]
            
            # 计算组内的统一缩放因子
            group_max = torch.max(torch.abs(group_weights))
            group_scale = group_max / self.max_int
            
            # 对组内所有专家使用相同的缩放因子
            for expert_idx in range(group_start, group_end):
                scales[expert_idx] = group_scale
                
                quantized_weight = torch.round(expert_weights[expert_idx] / group_scale).clamp(
                    self.min_int, self.max_int)
                quantized_weights[expert_idx] = quantized_weight.to(torch.int8)
        
        return quantized_weights, scales
    
    def adaptive_quantization(self, 
                             expert_weights: torch.Tensor,
                             expert_usage_freq: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        基于专家使用频率的自适应量化
        
        Args:
            expert_weights: [num_experts, output_dim, input_dim]
            expert_usage_freq: [num_experts] 专家使用频率
        """
        
        num_experts = expert_weights.shape[0]
        quantized_weights = torch.zeros_like(expert_weights, dtype=torch.int8)
        scales = torch.zeros(num_experts, dtype=expert_weights.dtype)
        
        # 根据使用频率分配量化精度
        freq_sorted_indices = torch.argsort(expert_usage_freq, descending=True)
        
        for i, expert_idx in enumerate(freq_sorted_indices):
            expert_weight = expert_weights[expert_idx]
            
            # 高频专家使用更精细的量化
            if i < num_experts // 4:  # 前25%的专家
                # 使用per-channel量化
                scale = self._per_channel_quantize(expert_weight)
            elif i < num_experts // 2:  # 前50%的专家
                # 使用per-tensor量化
                scale = torch.max(torch.abs(expert_weight)) / self.max_int
            else:  # 低频专家
                # 使用更粗糙的量化
                scale = torch.max(torch.abs(expert_weight)) / (self.max_int // 2)
            
            scales[expert_idx] = scale
            quantized_weight = torch.round(expert_weight / scale).clamp(self.min_int, self.max_int)
            quantized_weights[expert_idx] = quantized_weight.to(torch.int8)
        
        return quantized_weights, scales
    
    def _per_channel_quantize(self, weight: torch.Tensor) -> torch.Tensor:
        """Per-channel量化"""
        # 计算每个输出通道的缩放因子
        channel_max = torch.max(torch.abs(weight), dim=1, keepdim=True)[0]
        scale = channel_max / self.max_int
        return scale.squeeze()
```

#### 3.2 路由感知量化

```python
class RouterAwareQuantization:
    """路由感知量化"""
    
    def __init__(self, router_threshold: float = 0.01):
        self.router_threshold = router_threshold
    
    def analyze_routing_patterns(self, 
                                router_logits_history: List[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """分析路由模式"""
        
        # 收集所有路由决策
        all_router_probs = []
        for router_logits in router_logits_history:
            router_probs = F.softmax(router_logits, dim=-1)
            all_router_probs.append(router_probs)
        
        # 计算专家使用统计
        combined_probs = torch.cat(all_router_probs, dim=0)
        
        # 专家激活频率
        expert_activation_freq = torch.mean(combined_probs > self.router_threshold, dim=0)
        
        # 专家平均权重
        expert_avg_weights = torch.mean(combined_probs, dim=0)
        
        # 专家权重方差
        expert_weight_variance = torch.var(combined_probs, dim=0)
        
        return {
            "activation_frequency": expert_activation_freq,
            "average_weights": expert_avg_weights,
            "weight_variance": expert_weight_variance
        }
    
    def router_guided_quantization(self,
                                  expert_weights: torch.Tensor,
                                  routing_stats: Dict[str, torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor]:
        """基于路由统计的量化"""
        
        activation_freq = routing_stats["activation_frequency"]
        avg_weights = routing_stats["average_weights"]
        
        # 计算专家重要性分数
        importance_scores = activation_freq * avg_weights
        
        # 根据重要性分配量化精度
        num_experts = expert_weights.shape[0]
        quantized_weights = torch.zeros_like(expert_weights, dtype=torch.int8)
        scales = torch.zeros(num_experts, dtype=expert_weights.dtype)
        
        # 对重要性进行排序
        sorted_indices = torch.argsort(importance_scores, descending=True)
        
        for rank, expert_idx in enumerate(sorted_indices):
            expert_weight = expert_weights[expert_idx]
            
            # 重要专家使用更高精度
            if rank < num_experts * 0.2:  # 前20%
                # 8位量化
                max_val = 127
            elif rank < num_experts * 0.5:  # 前50%
                # 7位量化
                max_val = 63
            else:  # 其余专家
                # 6位量化
                max_val = 31
            
            # 计算缩放因子
            weight_max = torch.max(torch.abs(expert_weight))
            scale = weight_max / max_val
            scales[expert_idx] = scale
            
            # 量化
            quantized_weight = torch.round(expert_weight / scale).clamp(-max_val-1, max_val)
            quantized_weights[expert_idx] = quantized_weight.to(torch.int8)
        
        return quantized_weights, scales
```

## 实践任务

### 任务1: MoE基础实现

创建 `moe_implementation.py`:

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional

class SimpleMoELayer(nn.Module):
    """简单的MoE层实现"""
    
    def __init__(self,
                 hidden_size: int,
                 intermediate_size: int,
                 num_experts: int,
                 top_k: int = 2,
                 router_bias: bool = False):
        super().__init__()
        
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        self.num_experts = num_experts
        self.top_k = top_k
        
        # 路由网络
        self.router = nn.Linear(hidden_size, num_experts, bias=router_bias)
        
        # 专家网络
        self.experts = nn.ModuleList([
            self._create_expert() for _ in range(num_experts)
        ])
        
        # 负载均衡损失权重
        self.load_balance_loss_weight = 0.01
    
    def _create_expert(self) -> nn.Module:
        """创建单个专家网络"""
        return nn.Sequential(
            nn.Linear(self.hidden_size, self.intermediate_size),
            nn.ReLU(),
            nn.Linear(self.intermediate_size, self.hidden_size)
        )
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: [batch_size, seq_len, hidden_size]
        
        Returns:
            output: [batch_size, seq_len, hidden_size]
            load_balance_loss: 负载均衡损失
        """
        
        batch_size, seq_len, hidden_size = x.shape
        
        # 重塑为2D进行处理
        x_2d = x.view(-1, hidden_size)  # [batch_size * seq_len, hidden_size]
        
        # 路由计算
        router_logits = self.router(x_2d)  # [batch_size * seq_len, num_experts]
        router_probs = F.softmax(router_logits, dim=-1)
        
        # Top-K选择
        topk_probs, topk_indices = torch.topk(router_probs, self.top_k, dim=-1)
        topk_probs = topk_probs / torch.sum(topk_probs, dim=-1, keepdim=True)  # 重新归一化
        
        # 专家计算
        output = torch.zeros_like(x_2d)
        
        for i in range(self.top_k):
            expert_indices = topk_indices[:, i]  # [batch_size * seq_len]
            expert_weights = topk_probs[:, i]    # [batch_size * seq_len]
            
            # 为每个专家批量处理tokens
            for expert_id in range(self.num_experts):
                mask = (expert_indices == expert_id)
                if mask.any():
                    expert_input = x_2d[mask]
                    expert_output = self.experts[expert_id](expert_input)
                    output[mask] += expert_weights[mask].unsqueeze(-1) * expert_output
        
        # 计算负载均衡损失
        load_balance_loss = self._compute_load_balance_loss(router_probs)
        
        # 重塑回原始形状
        output = output.view(batch_size, seq_len, hidden_size)
        
        return output, load_balance_loss
    
    def _compute_load_balance_loss(self, router_probs: torch.Tensor) -> torch.Tensor:
        """计算负载均衡损失"""
        
        # 计算每个专家的平均概率
        expert_probs = torch.mean(router_probs, dim=0)  # [num_experts]
        
        # 计算每个专家被选中的频率
        expert_counts = torch.sum(router_probs > 0.01, dim=0).float()  # [num_experts]
        expert_freq = expert_counts / router_probs.shape[0]
        
        # 负载均衡损失：鼓励专家使用的均匀分布
        load_balance_loss = self.num_experts * torch.sum(expert_probs * expert_freq)
        
        return self.load_balance_loss_weight * load_balance_loss

class QuantizedMoELayer(SimpleMoELayer):
    """量化的MoE层"""
    
    def __init__(self, *args, quantization_bits: int = 8, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.quantization_bits = quantization_bits
        self.max_int = 2**(quantization_bits - 1) - 1
        self.min_int = -2**(quantization_bits - 1)
        
        # 量化参数存储
        self.register_buffer('expert_scales', torch.ones(self.num_experts))
        self.register_buffer('experts_quantized', torch.zeros(1))  # 占位符
        
        self.quantized = False
    
    def quantize_experts(self):
        """量化所有专家网络"""
        
        quantized_experts = []
        scales = []
        
        for expert_id, expert in enumerate(self.experts):
            # 获取专家的权重
            expert_weights = []
            for param in expert.parameters():
                expert_weights.append(param.data.flatten())
            
            all_weights = torch.cat(expert_weights)
            
            # 计算缩放因子
            weight_max = torch.max(torch.abs(all_weights))
            scale = weight_max / self.max_int
            scales.append(scale)
            
            # 量化专家权重
            quantized_expert = self._quantize_expert(expert, scale)
            quantized_experts.append(quantized_expert)
        
        # 保存量化信息
        self.expert_scales = torch.tensor(scales)
        self.quantized_experts = quantized_experts
        self.quantized = True
        
        print(f"专家网络量化完成，缩放因子范围: [{min(scales):.6f}, {max(scales):.6f}]")
    
    def _quantize_expert(self, expert: nn.Module, scale: float) -> nn.Module:
        """量化单个专家网络"""
        
        quantized_expert = type(expert)()
        
        for name, module in expert.named_children():
            if isinstance(module, nn.Linear):
                # 量化线性层
                quantized_linear = self._quantize_linear(module, scale)
                setattr(quantized_expert, name, quantized_linear)
            else:
                # 非线性层保持不变
                setattr(quantized_expert, name, module)
        
        return quantized_expert
    
    def _quantize_linear(self, linear: nn.Linear, scale: float) -> nn.Module:
        """量化线性层"""
        
        # 量化权重
        quantized_weight = torch.round(linear.weight.data / scale).clamp(
            self.min_int, self.max_int).to(torch.int8)
        
        # 创建量化线性层
        class QuantizedLinear(nn.Module):
            def __init__(self, quantized_weight, scale, bias):
                super().__init__()
                self.register_buffer('quantized_weight', quantized_weight)
                self.scale = scale
                if bias is not None:
                    self.register_buffer('bias', bias)
                else:
                    self.bias = None
            
            def forward(self, x):
                # 反量化并计算
                weight = self.quantized_weight.float() * self.scale
                output = F.linear(x, weight, self.bias)
                return output
        
        return QuantizedLinear(quantized_weight, scale, linear.bias)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """量化MoE前向传播"""
        
        if not self.quantized:
            # 如果未量化，使用原始实现
            return super().forward(x)
        
        # 使用量化专家进行计算
        batch_size, seq_len, hidden_size = x.shape
        x_2d = x.view(-1, hidden_size)
        
        # 路由计算（路由网络不量化）
        router_logits = self.router(x_2d)
        router_probs = F.softmax(router_logits, dim=-1)
        
        # Top-K选择
        topk_probs, topk_indices = torch.topk(router_probs, self.top_k, dim=-1)
        topk_probs = topk_probs / torch.sum(topk_probs, dim=-1, keepdim=True)
        
        # 使用量化专家计算
        output = torch.zeros_like(x_2d)
        
        for i in range(self.top_k):
            expert_indices = topk_indices[:, i]
            expert_weights = topk_probs[:, i]
            
            for expert_id in range(self.num_experts):
                mask = (expert_indices == expert_id)
                if mask.any():
                    expert_input = x_2d[mask]
                    expert_output = self.quantized_experts[expert_id](expert_input)
                    output[mask] += expert_weights[mask].unsqueeze(-1) * expert_output
        
        load_balance_loss = self._compute_load_balance_loss(router_probs)
        output = output.view(batch_size, seq_len, hidden_size)
        
        return output, load_balance_loss

def test_moe_implementation():
    """测试MoE实现"""
    
    print("=== MoE实现测试 ===")
    
    # 参数设置
    batch_size, seq_len, hidden_size = 4, 32, 512
    intermediate_size = 2048
    num_experts = 8
    top_k = 2
    
    # 创建测试数据
    x = torch.randn(batch_size, seq_len, hidden_size)
    
    # 测试标准MoE
    print("1. 标准MoE测试")
    moe_layer = SimpleMoELayer(hidden_size, intermediate_size, num_experts, top_k)
    
    output, load_balance_loss = moe_layer(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"负载均衡损失: {load_balance_loss:.6f}")
    
    # 测试量化MoE
    print("\n2. 量化MoE测试")
    quantized_moe = QuantizedMoELayer(hidden_size, intermediate_size, num_experts, top_k, quantization_bits=8)
    
    # 量化前的输出
    output_before, _ = quantized_moe(x)
    
    # 执行量化
    quantized_moe.quantize_experts()
    
    # 量化后的输出
    output_after, _ = quantized_moe(x)
    
    # 计算量化误差
    quantization_error = torch.mean(torch.abs(output_before - output_after))
    print(f"量化误差: {quantization_error:.6f}")
    
    # 内存使用对比
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    original_params = count_parameters(moe_layer)
    quantized_params = count_parameters(quantized_moe)
    
    print(f"\n3. 内存使用对比")
    print(f"原始模型参数: {original_params:,}")
    print(f"量化模型参数: {quantized_params:,}")
    print(f"理论压缩比: ~2x (FP16 -> INT8)")

if __name__ == "__main__":
    test_moe_implementation()
```

### 任务2: MoE量化策略对比

创建 `moe_quantization_strategies.py`:

```python
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

class MoEQuantizationComparison:
    """MoE量化策略对比"""
    
    def __init__(self, num_experts: int = 8, hidden_size: int = 512):
        self.num_experts = num_experts
        self.hidden_size = hidden_size
        
        # 创建模拟专家权重
        self.expert_weights = self._create_expert_weights()
        
        # 模拟路由统计
        self.routing_stats = self._simulate_routing_stats()
    
    def _create_expert_weights(self) -> torch.Tensor:
        """创建模拟的专家权重"""
        
        # 不同专家具有不同的权重分布特性
        expert_weights = []
        
        for i in range(self.num_experts):
            if i < 2:  # 前两个专家：权重较大
                weight = torch.randn(self.hidden_size, self.hidden_size) * 0.5
            elif i < 4:  # 中间专家：标准权重
                weight = torch.randn(self.hidden_size, self.hidden_size) * 0.1
            else:  # 后面专家：权重较小
                weight = torch.randn(self.hidden_size, self.hidden_size) * 0.02
            
            expert_weights.append(weight)
        
        return torch.stack(expert_weights)  # [num_experts, hidden_size, hidden_size]
    
    def _simulate_routing_stats(self) -> Dict[str, torch.Tensor]:
        """模拟路由统计信息"""
        
        # 模拟不均匀的专家使用模式
        activation_freq = torch.tensor([0.8, 0.6, 0.4, 0.3, 0.2, 0.1, 0.05, 0.02])
        avg_weights = torch.tensor([0.3, 0.25, 0.2, 0.15, 0.05, 0.03, 0.01, 0.01])
        weight_variance = torch.tensor([0.1, 0.08, 0.06, 0.04, 0.02, 0.01, 0.005, 0.002])
        
        return {
            "activation_frequency": activation_freq,
            "average_weights": avg_weights,
            "weight_variance": weight_variance
        }
    
    def uniform_quantization(self, bits: int = 8) -> Tuple[torch.Tensor, torch.Tensor, float]:
        """统一量化策略"""
        
        max_int = 2**(bits - 1) - 1
        min_int = -2**(bits - 1)
        
        # 全局缩放因子
        global_max = torch.max(torch.abs(self.expert_weights))
        global_scale = global_max / max_int
        
        # 量化所有专家
        quantized_weights = torch.round(self.expert_weights / global_scale).clamp(min_int, max_int)
        scales = torch.full((self.num_experts,), global_scale)
        
        # 计算量化误差
        dequantized_weights = quantized_weights * global_scale
        error = torch.mean(torch.abs(self.expert_weights - dequantized_weights)).item()
        
        return quantized_weights, scales, error
    
    def per_expert_quantization(self, bits: int = 8) -> Tuple[torch.Tensor, torch.Tensor, float]:
        """专家独立量化策略"""
        
        max_int = 2**(bits - 1) - 1
        min_int = -2**(bits - 1)
        
        quantized_weights = torch.zeros_like(self.expert_weights, dtype=torch.int8)
        scales = torch.zeros(self.num_experts)
        
        for i in range(self.num_experts):
            expert_weight = self.expert_weights[i]
            expert_max = torch.max(torch.abs(expert_weight))
            expert_scale = expert_max / max_int
            
            scales[i] = expert_scale
            quantized_weights[i] = torch.round(expert_weight / expert_scale).clamp(min_int, max_int)
        
        # 计算量化误差
        dequantized_weights = quantized_weights.float() * scales.unsqueeze(-1).unsqueeze(-1)
        error = torch.mean(torch.abs(self.expert_weights - dequantized_weights)).item()
        
        return quantized_weights, scales, error
    
    def importance_aware_quantization(self, bits: int = 8) -> Tuple[torch.Tensor, torch.Tensor, float]:
        """重要性感知量化策略"""
        
        # 计算专家重要性
        importance_scores = (self.routing_stats["activation_frequency"] * 
                           self.routing_stats["average_weights"])
        
        quantized_weights = torch.zeros_like(self.expert_weights, dtype=torch.int8)
        scales = torch.zeros(self.num_experts)
        
        # 根据重要性分配不同的量化精度
        sorted_indices = torch.argsort(importance_scores, descending=True)
        
        for rank, expert_idx in enumerate(sorted_indices):
            expert_weight = self.expert_weights[expert_idx]
            
            # 重要专家使用更高精度
            if rank < self.num_experts * 0.25:  # 前25%
                effective_bits = bits
            elif rank < self.num_experts * 0.5:  # 前50%
                effective_bits = bits - 1
            else:  # 其余专家
                effective_bits = bits - 2
            
            max_int = 2**(effective_bits - 1) - 1
            min_int = -2**(effective_bits - 1)
            
            expert_max = torch.max(torch.abs(expert_weight))
            expert_scale = expert_max / max_int
            
            scales[expert_idx] = expert_scale
            quantized_weights[expert_idx] = torch.round(expert_weight / expert_scale).clamp(min_int, max_int)
        
        # 计算量化误差
        dequantized_weights = quantized_weights.float() * scales.unsqueeze(-1).unsqueeze(-1)
        error = torch.mean(torch.abs(self.expert_weights - dequantized_weights)).item()
        
        return quantized_weights, scales, error
    
    def mixed_precision_quantization(self) -> Tuple[torch.Tensor, torch.Tensor, float]:
        """混合精度量化策略"""
        
        importance_scores = (self.routing_stats["activation_frequency"] * 
                           self.routing_stats["average_weights"])
        
        quantized_weights = []
        scales = []
        total_error = 0
        
        sorted_indices = torch.argsort(importance_scores, descending=True)
        
        for rank, expert_idx in enumerate(sorted_indices):
            expert_weight = self.expert_weights[expert_idx]
            
            # 根据重要性选择量化位数
            if rank < self.num_experts * 0.25:  # 前25%: 8位
                bits = 8
            elif rank < self.num_experts * 0.5:  # 前50%: 6位
                bits = 6
            else:  # 其余: 4位
                bits = 4
            
            max_int = 2**(bits - 1) - 1
            min_int = -2**(bits - 1)
            
            expert_max = torch.max(torch.abs(expert_weight))
            expert_scale = expert_max / max_int
            
            quantized_weight = torch.round(expert_weight / expert_scale).clamp(min_int, max_int)
            
            # 计算该专家的量化误差
            dequantized_weight = quantized_weight * expert_scale
            expert_error = torch.mean(torch.abs(expert_weight - dequantized_weight))
            total_error += expert_error * importance_scores[expert_idx]  # 加权误差
            
            quantized_weights.append(quantized_weight)
            scales.append(expert_scale)
        
        # 重新排序回原始顺序
        reordered_quantized = [None] * self.num_experts
        reordered_scales = [None] * self.num_experts
        
        for rank, expert_idx in enumerate(sorted_indices):
            reordered_quantized[expert_idx] = quantized_weights[rank]
            reordered_scales[expert_idx] = scales[rank]
        
        quantized_weights_tensor = torch.stack(reordered_quantized).to(torch.int8)
        scales_tensor = torch.tensor(reordered_scales)
        
        return quantized_weights_tensor, scales_tensor, total_error.item()
    
    def compare_strategies(self) -> Dict[str, Dict[str, float]]:
        """对比不同量化策略"""
        
        strategies = {
            "统一量化": self.uniform_quantization,
            "专家独立量化": self.per_expert_quantization,
            "重要性感知量化": self.importance_aware_quantization,
            "混合精度量化": self.mixed_precision_quantization,
        }
        
        results = {}
        
        print("=== MoE量化策略对比 ===")
        
        for strategy_name, strategy_func in strategies.items():
            _, scales, error = strategy_func()
            
            # 计算压缩比（简化计算）
            if strategy_name == "混合精度量化":
                # 混合精度的压缩比需要特殊计算
                compression_ratio = 2.0  # 简化估算
            else:
                compression_ratio = 2.0  # FP16 -> INT8
            
            results[strategy_name] = {
                "量化误差": error,
                "压缩比": compression_ratio,
                "缩放因子方差": torch.var(scales).item()
            }
            
            print(f"\n{strategy_name}:")
            print(f"  量化误差: {error:.6f}")
            print(f"  压缩比: {compression_ratio:.1f}x")
            print(f"  缩放因子方差: {torch.var(scales).item():.6f}")
        
        return results

def test_moe_quantization_strategies():
    """测试MoE量化策略"""
    
    comparison = MoEQuantizationComparison(num_experts=8, hidden_size=256)
    results = comparison.compare_strategies()
    
    # 可视化结果
    strategies = list(results.keys())
    errors = [results[s]["量化误差"] for s in strategies]
    compression_ratios = [results[s]["压缩比"] for s in strategies]
    
    print(f"\n=== 策略排名（按量化误差） ===")
    sorted_strategies = sorted(zip(strategies, errors), key=lambda x: x[1])
    for i, (strategy, error) in enumerate(sorted_strategies):
        print(f"{i+1}. {strategy}: {error:.6f}")

if __name__ == "__main__":
    test_moe_quantization_strategies()
```

## 学习检查点

### 第1天结束检查 (9.22)
- [ ] 理解MoE架构的基本原理和数学表示
- [ ] 掌握vLLM中FusedMoE的实现机制
- [ ] 学习专家网络的量化策略
- [ ] 完成基础MoE实现

### 第2天结束检查 (9.23)
- [ ] 深入理解路由感知量化技术
- [ ] 掌握不同量化策略的优缺点
- [ ] 完成MoE量化策略对比实验
- [ ] 能够选择合适的MoE量化方案

## 参考资料

### vLLM核心源码文件
1. `vllm/model_executor/layers/fused_moe/` - 融合MoE实现
2. `vllm/model_executor/layers/quantization/experts_int8.py` - 专家INT8量化
3. `vllm/model_executor/layers/quantization/moe_wna16.py` - MoE权重激活量化

### 技术论文
1. "Switch Transformer: Scaling to Trillion Parameter Models"
2. "GLaM: Efficient Scaling of Language Models with Mixture-of-Experts"
3. "PaLM: Scaling Language Modeling with Pathways"
4. "Quantizing Mixture-of-Experts Models"

## 下一步预告

完成MoE量化技术学习后，下一步将学习通信量化实现，重点关注：
- 分布式推理中的量化通信
- 梯度和激活值的量化传输
- 通信压缩算法
- vLLM中的分布式量化支持
