# vLLM量化算子深度学习文档

## 学习时间：9.10-9.12 (3天)

## 学习目标

1. 深入理解量化算子的CUDA实现原理
2. 掌握Quant/Dequant算子的优化策略
3. 学习LayerNorm融合算子的设计思想
4. 理解GEMM量化算子的性能优化技术
5. 掌握Triton kernel的编写和调优方法

## 量化算子架构概览

### 1. vLLM量化算子分类

vLLM中的量化算子主要分为以下几类：

```
量化算子体系
├── 基础量化算子
│   ├── Quant/Dequant算子
│   ├── Scale计算算子
│   └── 数据类型转换算子
├── 融合算子
│   ├── LayerNorm + Quantization
│   ├── GEMM + Quantization
│   └── Attention + KV Quantization
├── 专用算子
│   ├── AWQ算子
│   ├── GPTQ算子
│   └── FP8算子
└── 优化算子
    ├── Triton实现
    ├── CUTLASS实现
    └── 自定义CUDA kernel
```

### 2. 核心算子源码分析

#### 2.1 基础量化算子

```python
# 来源: vllm/model_executor/layers/quantization/utils/quant_utils.py
def quantize_weights(weight: torch.Tensor,
                    quant_type: str,
                    group_size: int = -1,
                    zero_point: bool = True) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    权重量化的通用函数
    
    Args:
        weight: 原始权重张量
        quant_type: 量化类型 ("int4", "int8", "fp8")
        group_size: 分组大小，-1表示per-tensor量化
        zero_point: 是否使用零点偏移
    
    Returns:
        quantized_weight: 量化后的权重
        scales: 量化缩放因子
        zero_points: 零点偏移（如果使用）
    """
    
    if quant_type == "int4":
        return quantize_int4_weights(weight, group_size, zero_point)
    elif quant_type == "int8":
        return quantize_int8_weights(weight, group_size, zero_point)
    elif quant_type == "fp8":
        return quantize_fp8_weights(weight, group_size)
    else:
        raise ValueError(f"Unsupported quantization type: {quant_type}")

def quantize_int8_weights(weight: torch.Tensor, 
                         group_size: int = -1,
                         zero_point: bool = True) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """INT8权重量化"""
    
    if group_size == -1:
        # Per-tensor量化
        if zero_point:
            # 非对称量化
            w_min = weight.min()
            w_max = weight.max()
            scale = (w_max - w_min) / 255.0
            zp = torch.round(-w_min / scale).clamp(0, 255)
            
            quantized = torch.round(weight / scale + zp).clamp(0, 255).to(torch.uint8)
            return quantized, scale.unsqueeze(0), zp.unsqueeze(0)
        else:
            # 对称量化
            scale = weight.abs().max() / 127.0
            quantized = torch.round(weight / scale).clamp(-128, 127).to(torch.int8)
            return quantized, scale.unsqueeze(0), torch.zeros(1, dtype=weight.dtype)
    else:
        # Per-group量化
        return quantize_int8_per_group(weight, group_size, zero_point)

def quantize_int8_per_group(weight: torch.Tensor,
                           group_size: int,
                           zero_point: bool) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """分组INT8量化"""
    
    # 重塑权重为分组形状
    original_shape = weight.shape
    weight_grouped = weight.view(-1, group_size)
    
    scales = []
    zero_points = []
    quantized_groups = []
    
    for group in weight_grouped:
        if zero_point:
            # 非对称量化
            g_min = group.min()
            g_max = group.max()
            scale = (g_max - g_min) / 255.0
            zp = torch.round(-g_min / scale).clamp(0, 255)
            
            q_group = torch.round(group / scale + zp).clamp(0, 255).to(torch.uint8)
        else:
            # 对称量化
            scale = group.abs().max() / 127.0
            zp = torch.tensor(0.0, dtype=group.dtype)
            
            q_group = torch.round(group / scale).clamp(-128, 127).to(torch.int8)
        
        scales.append(scale)
        zero_points.append(zp)
        quantized_groups.append(q_group)
    
    # 重塑回原始形状
    quantized_weight = torch.stack(quantized_groups).view(original_shape)
    scales_tensor = torch.stack(scales)
    zero_points_tensor = torch.stack(zero_points)
    
    return quantized_weight, scales_tensor, zero_points_tensor
```

#### 2.2 FP8量化算子实现

```python
# 来源: vllm/model_executor/layers/quantization/utils/w8a8_utils.py
class Fp8LinearOp:
    """FP8线性层算子"""
    
    @staticmethod
    def forward(input: torch.Tensor,
                weight: torch.Tensor,
                weight_scale: torch.Tensor,
                input_scale: Optional[torch.Tensor] = None,
                bias: Optional[torch.Tensor] = None,
                use_per_token_if_dynamic: bool = False) -> torch.Tensor:
        """FP8线性层前向传播"""
        
        # 动态量化输入（如果需要）
        if input_scale is None:
            input_fp8, input_scale = dynamically_quantize_per_token(input)
        else:
            input_fp8 = input.to(torch.float8_e4m3fn)
        
        # 执行FP8 GEMM
        output = torch.ops._C.fp8_gemm(
            input_fp8,
            weight,  # 已经是FP8格式
            input_scale,
            weight_scale,
            torch.float16,  # 输出数据类型
            use_per_token_if_dynamic
        )
        
        # 添加偏置
        if bias is not None:
            output = output + bias
            
        return output

def dynamically_quantize_per_token(x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
    """Per-token动态FP8量化"""
    
    # 计算每个token的缩放因子
    x_amax = x.abs().amax(dim=-1, keepdim=True)
    scale = x_amax / torch.finfo(torch.float8_e4m3fn).max
    
    # 避免除零
    scale = scale.clamp(min=torch.finfo(torch.float8_e4m3fn).tiny)
    
    # 量化
    x_scaled = x / scale
    x_fp8 = x_scaled.to(torch.float8_e4m3fn)
    
    return x_fp8, scale.squeeze(-1)

def per_tensor_dequantize(tensor: torch.Tensor, 
                         inv_scale: torch.Tensor) -> torch.Tensor:
    """Per-tensor反量化"""
    fake_qweight = tensor.to(torch.float16)
    dq_weight = fake_qweight * inv_scale
    return dq_weight
```

#### 2.3 LayerNorm融合算子

```python
# 来源: vllm/model_executor/layers/layernorm.py (量化相关部分)
class QuantizedRMSNorm(nn.Module):
    """量化RMS归一化层"""
    
    def __init__(self,
                 hidden_size: int,
                 eps: float = 1e-6,
                 quant_config: Optional[QuantizationConfig] = None):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(hidden_size))
        self.variance_epsilon = eps
        self.quant_config = quant_config
        
        # 如果启用量化，创建量化方法
        if quant_config is not None:
            self.quant_method = quant_config.get_quant_method(self, prefix="")
            if hasattr(self.quant_method, 'create_weights'):
                self.quant_method.create_weights(self)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播，集成量化"""
        
        # 标准RMS归一化
        variance = x.pow(2).mean(-1, keepdim=True)
        hidden_states = x * torch.rsqrt(variance + self.variance_epsilon)
        
        # 应用权重
        output = self.weight * hidden_states
        
        # 如果启用量化，应用量化方法
        if hasattr(self, 'quant_method'):
            output = self.quant_method.apply(output)
        
        return output

# Triton实现的融合LayerNorm + Quantization kernel
@triton.jit
def fused_layernorm_quantize_kernel(
    x_ptr,  # 输入指针
    weight_ptr,  # 权重指针
    output_ptr,  # 输出指针
    scale_ptr,  # 缩放因子指针
    M,  # 批次大小
    N,  # 隐藏维度
    eps,  # epsilon
    BLOCK_SIZE: tl.constexpr,
):
    """融合的LayerNorm + 量化kernel"""
    
    # 获取程序ID
    pid = tl.program_id(0)
    
    # 计算偏移量
    row_start = pid * N
    offsets = row_start + tl.arange(0, BLOCK_SIZE)
    mask = offsets < (pid + 1) * N
    
    # 加载输入数据
    x = tl.load(x_ptr + offsets, mask=mask, other=0.0)
    weight = tl.load(weight_ptr + tl.arange(0, BLOCK_SIZE), mask=tl.arange(0, BLOCK_SIZE) < N, other=1.0)
    
    # 计算均值和方差
    mean = tl.sum(x, axis=0) / N
    x_centered = x - mean
    variance = tl.sum(x_centered * x_centered, axis=0) / N
    
    # LayerNorm
    inv_std = 1.0 / tl.sqrt(variance + eps)
    normalized = x_centered * inv_std * weight
    
    # 动态量化
    abs_max = tl.max(tl.abs(normalized), axis=0)
    scale = abs_max / 127.0
    quantized = tl.clamp(normalized / scale, -128.0, 127.0)
    
    # 存储结果
    tl.store(output_ptr + offsets, quantized.to(tl.int8), mask=mask)
    tl.store(scale_ptr + pid, scale)
```

### 3. GEMM量化算子优化

#### 3.1 AWQ GEMM实现

```python
# 来源: vllm/model_executor/layers/quantization/awq.py
class AWQLinearMethod(LinearMethodBase):
    """AWQ量化线性层方法"""
    
    def __init__(self, quant_config: AWQConfig):
        self.quant_config = quant_config
        
    def create_weights(self,
                      layer: torch.nn.Module,
                      input_size_per_partition: int,
                      output_partition_sizes: List[int],
                      input_size: int,
                      output_size: int,
                      params_dtype: torch.dtype,
                      **kwargs):
        """创建AWQ量化权重"""
        
        # 量化权重参数
        qweight = ModelWeightParameter(
            data=torch.empty(
                input_size_per_partition // 8,  # 4bit打包
                output_size,
                dtype=torch.int32,
            ),
            input_dim=0,
            output_dim=1,
            packed_dim=0,
            packed_factor=8,
        )
        
        # 缩放因子
        scales = ModelWeightParameter(
            data=torch.empty(
                input_size_per_partition // self.quant_config.group_size,
                output_size,
                dtype=params_dtype,
            ),
            input_dim=0,
            output_dim=1,
        )
        
        # 零点（如果使用）
        if self.quant_config.zero_point:
            qzeros = ModelWeightParameter(
                data=torch.empty(
                    input_size_per_partition // self.quant_config.group_size // 8,
                    output_size,
                    dtype=torch.int32,
                ),
                input_dim=0,
                output_dim=1,
                packed_dim=0,
                packed_factor=8,
            )
            layer.register_parameter("qzeros", qzeros)
        
        layer.register_parameter("qweight", qweight)
        layer.register_parameter("scales", scales)
    
    def apply(self,
              layer: torch.nn.Module,
              x: torch.Tensor,
              bias: Optional[torch.Tensor] = None) -> torch.Tensor:
        """应用AWQ量化线性变换"""
        
        # 获取量化参数
        qweight = layer.qweight
        scales = layer.scales
        qzeros = getattr(layer, "qzeros", None)
        
        # 调用AWQ GEMM kernel
        if qzeros is not None:
            output = torch.ops._C.awq_gemm(
                x, qweight, scales, qzeros,
                self.quant_config.group_size
            )
        else:
            output = torch.ops._C.awq_gemm_symmetric(
                x, qweight, scales,
                self.quant_config.group_size
            )
        
        if bias is not None:
            output = output + bias
            
        return output
```

#### 3.2 Marlin量化GEMM

```python
# 来源: vllm/model_executor/layers/quantization/utils/marlin_utils.py
def marlin_quantize(w: torch.Tensor,
                   quant_type: ScalarType,
                   group_size: int = -1) -> Tuple[torch.Tensor, torch.Tensor]:
    """Marlin格式量化"""
    
    assert quant_type.is_integer(), f"Marlin only supports integer types, got {quant_type}"
    
    # 获取量化位数
    num_bits = quant_type.size_bits
    max_q_val = 2**num_bits - 1
    
    if group_size == -1:
        # Per-channel量化
        w_ref = w.clone()
        q_w, q_s, q_zp = gptq_quantize_weights(w_ref, num_bits, group_size=-1)
    else:
        # Per-group量化
        q_w, q_s, q_zp = gptq_quantize_weights(w, num_bits, group_size)
    
    # 转换为Marlin格式
    q_w_marlin = marlin_permute_weights(q_w, num_bits)
    q_s_marlin = marlin_permute_scales(q_s, group_size, num_bits)
    
    return q_w_marlin, q_s_marlin

def apply_marlin_linear(input: torch.Tensor,
                       weight: torch.Tensor,
                       scales: torch.Tensor,
                       workspace: torch.Tensor,
                       num_bits: int,
                       group_size: int,
                       is_k_full: bool = True) -> torch.Tensor:
    """应用Marlin量化线性变换"""
    
    # 调用Marlin GEMM kernel
    output = torch.ops._C.marlin_gemm(
        input,
        weight,
        scales,
        workspace,
        num_bits,
        group_size,
        is_k_full
    )
    
    return output
```

## 实践任务

### 任务1: 基础量化算子实现

创建 `quantization_kernels.py`:

```python
import torch
import triton
import triton.language as tl

@triton.jit
def quantize_int8_kernel(
    input_ptr,
    output_ptr,
    scale_ptr,
    n_elements,
    BLOCK_SIZE: tl.constexpr,
):
    """INT8量化Triton kernel"""
    
    # 获取程序ID和偏移量
    pid = tl.program_id(axis=0)
    block_start = pid * BLOCK_SIZE
    offsets = block_start + tl.arange(0, BLOCK_SIZE)
    mask = offsets < n_elements
    
    # 加载输入数据
    input_vals = tl.load(input_ptr + offsets, mask=mask)
    
    # 计算缩放因子（简化版本，实际应该是全局最大值）
    abs_vals = tl.abs(input_vals)
    local_max = tl.max(abs_vals, axis=0)
    scale = local_max / 127.0
    
    # 量化
    quantized = tl.clamp(input_vals / scale, -128.0, 127.0)
    
    # 存储结果
    tl.store(output_ptr + offsets, quantized.to(tl.int8), mask=mask)
    
    # 存储缩放因子（只有第一个block存储）
    if pid == 0:
        tl.store(scale_ptr, scale)

def quantize_int8_triton(input_tensor: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
    """使用Triton实现INT8量化"""
    
    # 准备输出张量
    output = torch.empty_like(input_tensor, dtype=torch.int8)
    scale = torch.empty(1, dtype=input_tensor.dtype, device=input_tensor.device)
    
    # 计算网格大小
    n_elements = input_tensor.numel()
    BLOCK_SIZE = 1024
    grid = (triton.cdiv(n_elements, BLOCK_SIZE),)
    
    # 启动kernel
    quantize_int8_kernel[grid](
        input_tensor,
        output,
        scale,
        n_elements,
        BLOCK_SIZE=BLOCK_SIZE,
    )
    
    return output, scale

@triton.jit
def dequantize_int8_kernel(
    input_ptr,
    scale_ptr,
    output_ptr,
    n_elements,
    BLOCK_SIZE: tl.constexpr,
):
    """INT8反量化Triton kernel"""
    
    pid = tl.program_id(axis=0)
    block_start = pid * BLOCK_SIZE
    offsets = block_start + tl.arange(0, BLOCK_SIZE)
    mask = offsets < n_elements
    
    # 加载数据
    quantized_vals = tl.load(input_ptr + offsets, mask=mask)
    scale = tl.load(scale_ptr)
    
    # 反量化
    dequantized = quantized_vals.to(tl.float16) * scale
    
    # 存储结果
    tl.store(output_ptr + offsets, dequantized, mask=mask)

def test_quantization_kernels():
    """测试量化kernel"""
    
    # 创建测试数据
    input_tensor = torch.randn(10000, dtype=torch.float16, device='cuda')
    
    print("=== Triton量化kernel测试 ===")
    
    # Triton量化
    quantized, scale = quantize_int8_triton(input_tensor)
    
    print(f"原始数据范围: [{input_tensor.min():.4f}, {input_tensor.max():.4f}]")
    print(f"量化数据范围: [{quantized.min()}, {quantized.max()}]")
    print(f"缩放因子: {scale.item():.6f}")
    
    # 反量化
    output = torch.empty_like(input_tensor)
    n_elements = input_tensor.numel()
    BLOCK_SIZE = 1024
    grid = (triton.cdiv(n_elements, BLOCK_SIZE),)
    
    dequantize_int8_kernel[grid](
        quantized,
        scale,
        output,
        n_elements,
        BLOCK_SIZE=BLOCK_SIZE,
    )
    
    # 计算误差
    error = torch.mean(torch.abs(input_tensor - output))
    print(f"量化误差: {error:.6f}")

if __name__ == "__main__":
    test_quantization_kernels()
```

### 任务2: 融合算子实现

创建 `fused_operators.py`:

```python
import torch
import torch.nn as nn
import triton
import triton.language as tl

@triton.jit
def fused_linear_quantize_kernel(
    input_ptr,
    weight_ptr,
    bias_ptr,
    output_ptr,
    scale_ptr,
    M, N, K,
    stride_im, stride_ik,
    stride_wk, stride_wn,
    BLOCK_SIZE_M: tl.constexpr,
    BLOCK_SIZE_N: tl.constexpr,
    BLOCK_SIZE_K: tl.constexpr,
):
    """融合的线性变换 + 量化kernel"""
    
    # 获取程序ID
    pid_m = tl.program_id(0)
    pid_n = tl.program_id(1)
    
    # 计算偏移量
    offs_m = pid_m * BLOCK_SIZE_M + tl.arange(0, BLOCK_SIZE_M)
    offs_n = pid_n * BLOCK_SIZE_N + tl.arange(0, BLOCK_SIZE_N)
    offs_k = tl.arange(0, BLOCK_SIZE_K)
    
    # 初始化累加器
    accumulator = tl.zeros((BLOCK_SIZE_M, BLOCK_SIZE_N), dtype=tl.float32)
    
    # 矩阵乘法循环
    for k in range(0, K, BLOCK_SIZE_K):
        # 加载输入和权重
        input_ptrs = input_ptr + (offs_m[:, None] * stride_im + (k + offs_k)[None, :] * stride_ik)
        weight_ptrs = weight_ptr + ((k + offs_k)[:, None] * stride_wk + offs_n[None, :] * stride_wn)
        
        input_mask = (offs_m[:, None] < M) & ((k + offs_k)[None, :] < K)
        weight_mask = ((k + offs_k)[:, None] < K) & (offs_n[None, :] < N)
        
        a = tl.load(input_ptrs, mask=input_mask, other=0.0)
        b = tl.load(weight_ptrs, mask=weight_mask, other=0.0)
        
        # 累加
        accumulator += tl.dot(a, b)
    
    # 添加偏置
    if bias_ptr is not None:
        bias = tl.load(bias_ptr + offs_n, mask=offs_n < N, other=0.0)
        accumulator += bias[None, :]
    
    # 量化
    abs_max = tl.max(tl.abs(accumulator), axis=1)
    scale = abs_max / 127.0
    quantized = tl.clamp(accumulator / scale[:, None], -128.0, 127.0)
    
    # 存储结果
    output_ptrs = output_ptr + (offs_m[:, None] * N + offs_n[None, :])
    scale_ptrs = scale_ptr + offs_m
    
    output_mask = (offs_m[:, None] < M) & (offs_n[None, :] < N)
    scale_mask = offs_m < M
    
    tl.store(output_ptrs, quantized.to(tl.int8), mask=output_mask)
    tl.store(scale_ptrs, scale, mask=scale_mask)

class FusedLinearQuantize(nn.Module):
    """融合的线性层 + 量化"""
    
    def __init__(self, in_features: int, out_features: int, bias: bool = True):
        super().__init__()
        self.in_features = in_features
        self.out_features = out_features
        
        self.weight = nn.Parameter(torch.randn(out_features, in_features))
        if bias:
            self.bias = nn.Parameter(torch.randn(out_features))
        else:
            self.register_parameter('bias', None)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播，返回量化结果和缩放因子"""
        
        M, K = x.shape
        N = self.out_features
        
        # 准备输出张量
        output = torch.empty((M, N), dtype=torch.int8, device=x.device)
        scale = torch.empty(M, dtype=x.dtype, device=x.device)
        
        # 计算网格大小
        BLOCK_SIZE_M = 64
        BLOCK_SIZE_N = 64
        BLOCK_SIZE_K = 32
        
        grid = (triton.cdiv(M, BLOCK_SIZE_M), triton.cdiv(N, BLOCK_SIZE_N))
        
        # 启动kernel
        fused_linear_quantize_kernel[grid](
            x, self.weight, self.bias, output, scale,
            M, N, K,
            x.stride(0), x.stride(1),
            self.weight.stride(0), self.weight.stride(1),
            BLOCK_SIZE_M=BLOCK_SIZE_M,
            BLOCK_SIZE_N=BLOCK_SIZE_N,
            BLOCK_SIZE_K=BLOCK_SIZE_K,
        )
        
        return output, scale

def test_fused_operators():
    """测试融合算子"""
    
    # 创建测试数据
    batch_size, in_features, out_features = 128, 512, 256
    x = torch.randn(batch_size, in_features, dtype=torch.float16, device='cuda')
    
    # 创建融合层
    fused_layer = FusedLinearQuantize(in_features, out_features).cuda().half()
    
    print("=== 融合算子测试 ===")
    
    # 前向传播
    quantized_output, scales = fused_layer(x)
    
    print(f"输入形状: {x.shape}")
    print(f"量化输出形状: {quantized_output.shape}, 数据类型: {quantized_output.dtype}")
    print(f"缩放因子形状: {scales.shape}")
    print(f"量化输出范围: [{quantized_output.min()}, {quantized_output.max()}]")
    
    # 对比标准实现
    standard_layer = nn.Linear(in_features, out_features).cuda().half()
    standard_layer.weight.data = fused_layer.weight.data
    standard_layer.bias.data = fused_layer.bias.data
    
    standard_output = standard_layer(x)
    
    # 反量化融合输出进行对比
    dequantized_output = quantized_output.float() * scales.unsqueeze(1)
    
    error = torch.mean(torch.abs(standard_output - dequantized_output))
    print(f"与标准实现的误差: {error:.6f}")

if __name__ == "__main__":
    test_fused_operators()
```

## 学习检查点

### 第1天结束检查 (9.10)
- [ ] 理解vLLM量化算子的分类和架构
- [ ] 掌握基础量化算子的实现原理
- [ ] 学习FP8量化算子的优化技术
- [ ] 完成基础量化kernel实现

### 第2天结束检查 (9.11)
- [ ] 深入理解LayerNorm融合算子设计
- [ ] 掌握AWQ和Marlin GEMM实现
- [ ] 学习Triton kernel编程技巧
- [ ] 完成融合算子实现

### 第3天结束检查 (9.12)
- [ ] 理解CUTLASS集成和优化策略
- [ ] 掌握算子性能调优方法
- [ ] 完成算子性能对比测试
- [ ] 能够编写自定义量化算子

## 参考资料

### vLLM核心源码文件
1. `vllm/model_executor/layers/quantization/kernels/` - 量化kernel实现
2. `vllm/model_executor/layers/quantization/utils/` - 量化工具函数
3. `vllm/_custom_ops.py` - 自定义算子接口
4. `vllm/triton_utils/` - Triton工具函数

### 技术文档
1. [Triton编程指南](https://triton-lang.org/main/programming-guide/index.html)
2. [CUTLASS文档](https://github.com/NVIDIA/cutlass)
3. [CUDA编程指南](https://docs.nvidia.com/cuda/cuda-c-programming-guide/)

## 下一步预告

完成量化算子学习后，下一步将学习稀疏与压缩技术，重点关注：
- 结构化稀疏vs非结构化稀疏
- 压缩存储格式和算法
- 稀疏矩阵乘法优化
- vLLM中的稀疏支持机制
