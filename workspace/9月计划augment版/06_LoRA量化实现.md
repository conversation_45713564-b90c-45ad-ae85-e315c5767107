# LoRA量化实现深度学习文档

## 学习时间：9.16-9.17 (2天)

## 学习目标

1. 深入理解LoRA的数学原理和实现机制
2. 掌握量化LoRA权重的策略和技术细节
3. 学习多LoRA适配器的管理和切换机制
4. 理解vLLM中LoRA的集成方式和优化策略
5. 掌握LoRA与量化的结合使用方法

## LoRA基础理论

### 1. LoRA数学原理

#### 1.1 低秩分解概念

LoRA (Low-Rank Adaptation) 基于这样的假设：大型预训练模型的权重更新具有低秩特性。

```
原始权重更新: ΔW ∈ R^(d×k)
低秩分解: ΔW = A × B
其中: A ∈ R^(d×r), B ∈ R^(r×k), r << min(d,k)
```

#### 1.2 LoRA前向传播

```python
# 标准线性层
h = x @ W

# LoRA增强的线性层
h = x @ W + x @ (A @ B) * scaling
# 其中 scaling = α / r (α是可学习参数，r是秩)
```

### 2. vLLM中的LoRA实现架构

#### 2.1 LoRA配置系统

```python
# 来源: vllm/lora/request.py
@dataclass
class LoRARequest:
    """LoRA请求配置"""
    
    lora_name: str
    lora_int_id: int
    lora_local_path: str
    lora_scaling: float = 1.0
    long_lora_max_len: Optional[int] = None

# 来源: vllm/config.py
@dataclass
class LoRAConfig:
    """LoRA全局配置"""
    
    max_lora_rank: int = 16
    max_loras: int = 1
    max_cpu_loras: Optional[int] = None
    lora_extra_vocab_size: int = 256
    lora_dtype: Optional[Union[torch.dtype, str]] = None
    max_lora_rank: int = 16
    bias_enabled: bool = False
    
    def __post_init__(self):
        # 验证配置参数
        if self.max_lora_rank <= 0:
            raise ValueError("max_lora_rank must be positive")
        if self.max_loras <= 0:
            raise ValueError("max_loras must be positive")
```

#### 2.2 LoRA层实现

```python
# 来源: vllm/lora/layers.py
class LoRAMapping:
    """LoRA映射管理"""
    
    def __init__(self,
                 index_mapping: List[int],
                 prompt_mapping: List[int],
                 lora_ids: Set[int],
                 lora_index_mapping: Dict[int, int],
                 embeddings_tensor_2d: Optional[torch.Tensor] = None,
                 indices_len: Optional[List[int]] = None):
        
        self.index_mapping = index_mapping
        self.prompt_mapping = prompt_mapping
        self.lora_ids = lora_ids
        self.lora_index_mapping = lora_index_mapping
        self.embeddings_tensor_2d = embeddings_tensor_2d
        self.indices_len = indices_len

class BaseLayerWithLoRA(nn.Module):
    """带LoRA支持的基础层"""
    
    def __init__(self):
        super().__init__()
        self.lora_a_stacked = None  # A矩阵堆叠
        self.lora_b_stacked = None  # B矩阵堆叠
        self.lora_bias = None       # LoRA偏置
        
    def create_lora_weights(self,
                           max_loras: int,
                           lora_config: LoRAConfig,
                           model_config: Optional[PretrainedConfig] = None) -> None:
        """创建LoRA权重参数"""
        
        # A矩阵 (输入维度 -> 秩)
        self.lora_a_stacked = nn.Parameter(
            torch.zeros(
                max_loras,
                1,  # 适配器数量维度
                lora_config.max_lora_rank,
                self.input_size,
            ),
            requires_grad=False,
        )
        
        # B矩阵 (秩 -> 输出维度)
        self.lora_b_stacked = nn.Parameter(
            torch.zeros(
                max_loras,
                1,
                self.output_size,
                lora_config.max_lora_rank,
            ),
            requires_grad=False,
        )
        
        # LoRA偏置（如果启用）
        if lora_config.bias_enabled:
            self.lora_bias = nn.Parameter(
                torch.zeros(max_loras, 1, self.output_size),
                requires_grad=False,
            )
    
    def reset_lora(self, index: int):
        """重置指定索引的LoRA权重"""
        self.lora_a_stacked.data[index] = 0
        self.lora_b_stacked.data[index] = 0
        if self.lora_bias is not None:
            self.lora_bias.data[index] = 0
    
    def set_lora(self,
                 index: int,
                 lora_a: torch.Tensor,
                 lora_b: torch.Tensor,
                 embeddings_tensor: Optional[torch.Tensor] = None):
        """设置指定索引的LoRA权重"""
        
        self.reset_lora(index)
        
        # 设置A矩阵
        if lora_a.shape[-1] != self.input_size:
            raise ValueError(f"LoRA A matrix shape mismatch: {lora_a.shape} vs expected (..., {self.input_size})")
        
        self.lora_a_stacked.data[index,
                                 0, :lora_a.shape[-2], :lora_a.shape[-1]] = lora_a
        
        # 设置B矩阵
        if lora_b.shape[-1] != lora_a.shape[-2]:
            raise ValueError(f"LoRA rank mismatch: A={lora_a.shape}, B={lora_b.shape}")
        
        self.lora_b_stacked.data[index,
                                 0, :lora_b.shape[-2], :lora_b.shape[-1]] = lora_b

class VocabParallelEmbeddingWithLoRA(BaseLayerWithLoRA):
    """支持LoRA的词嵌入层"""
    
    def __init__(self, base_layer: VocabParallelEmbedding):
        super().__init__()
        self.base_layer = base_layer
        
    def forward(self, x: torch.Tensor, lora_mapping: LoRAMapping) -> torch.Tensor:
        """前向传播，集成LoRA"""
        
        # 基础嵌入
        output = self.base_layer(x)
        
        # 应用LoRA
        if lora_mapping and self.lora_a_stacked is not None:
            output = output + self._apply_lora_embedding(x, lora_mapping)
        
        return output
    
    def _apply_lora_embedding(self, x: torch.Tensor, lora_mapping: LoRAMapping) -> torch.Tensor:
        """应用LoRA到嵌入层"""
        
        # 获取LoRA嵌入
        embeddings_tensor_2d = lora_mapping.embeddings_tensor_2d
        if embeddings_tensor_2d is None:
            return torch.zeros_like(self.base_layer(x))
        
        # 应用LoRA变换
        lora_output = F.embedding(
            x.flatten(),
            embeddings_tensor_2d,
            padding_idx=None,
            max_norm=None,
            norm_type=2.0,
            scale_grad_by_freq=False,
            sparse=False
        )
        
        return lora_output.view_as(self.base_layer(x))

class ColumnParallelLinearWithLoRA(BaseLayerWithLoRA):
    """支持LoRA的列并行线性层"""
    
    def __init__(self, base_layer: ColumnParallelLinear):
        super().__init__()
        self.base_layer = base_layer
        self.input_size = base_layer.input_size
        self.output_size = base_layer.output_size_per_partition
        
    def forward(self, input_: torch.Tensor, lora_mapping: LoRAMapping) -> torch.Tensor:
        """前向传播，集成LoRA"""
        
        # 基础线性变换
        output, bias = self.base_layer.linear_method.apply(self.base_layer, input_)
        
        # 应用LoRA
        if lora_mapping and self.lora_a_stacked is not None:
            lora_output = self._apply_lora_linear(input_, lora_mapping)
            output = output + lora_output
        
        return output, bias
    
    def _apply_lora_linear(self, input_: torch.Tensor, lora_mapping: LoRAMapping) -> torch.Tensor:
        """应用LoRA到线性层"""
        
        # 获取批次中的LoRA索引
        indices = lora_mapping.index_mapping
        
        if not indices:
            return torch.zeros(
                input_.shape[0], self.output_size,
                dtype=input_.dtype, device=input_.device
            )
        
        # 批量LoRA计算
        # input_: [batch_size, input_size]
        # lora_a_stacked: [max_loras, 1, rank, input_size]
        # lora_b_stacked: [max_loras, 1, output_size, rank]
        
        # 选择当前批次使用的LoRA权重
        lora_a_current = self.lora_a_stacked[indices]  # [batch_size, 1, rank, input_size]
        lora_b_current = self.lora_b_stacked[indices]  # [batch_size, 1, output_size, rank]
        
        # 计算 input @ A^T
        # input_: [batch_size, input_size] -> [batch_size, 1, 1, input_size]
        input_expanded = input_.unsqueeze(1).unsqueeze(1)
        
        # [batch_size, 1, 1, input_size] @ [batch_size, 1, input_size, rank] -> [batch_size, 1, 1, rank]
        intermediate = torch.matmul(input_expanded, lora_a_current.transpose(-2, -1))
        
        # 计算 (input @ A^T) @ B^T
        # [batch_size, 1, 1, rank] @ [batch_size, 1, rank, output_size] -> [batch_size, 1, 1, output_size]
        lora_output = torch.matmul(intermediate, lora_b_current.transpose(-2, -1))
        
        # 压缩维度: [batch_size, 1, 1, output_size] -> [batch_size, output_size]
        return lora_output.squeeze(1).squeeze(1)
```

### 3. LoRA量化策略

#### 3.1 LoRA权重量化

```python
# LoRA权重量化实现
class QuantizedLoRALinear(BaseLayerWithLoRA):
    """量化的LoRA线性层"""
    
    def __init__(self, 
                 base_layer: ColumnParallelLinear,
                 lora_config: LoRAConfig,
                 quant_config: QuantizationConfig):
        super().__init__()
        self.base_layer = base_layer
        self.lora_config = lora_config
        self.quant_config = quant_config
        
        # LoRA量化参数
        self.lora_a_scales = None
        self.lora_b_scales = None
        
    def create_lora_weights(self, max_loras: int, lora_config: LoRAConfig) -> None:
        """创建量化的LoRA权重"""
        
        # 根据量化配置选择数据类型
        if isinstance(self.quant_config, Fp8Config):
            lora_dtype = torch.float8_e4m3fn
        elif hasattr(self.quant_config, 'weight_bits') and self.quant_config.weight_bits == 8:
            lora_dtype = torch.int8
        else:
            lora_dtype = torch.float16
        
        # 量化的A矩阵
        self.lora_a_stacked = nn.Parameter(
            torch.zeros(
                max_loras, 1, lora_config.max_lora_rank, self.input_size,
                dtype=lora_dtype
            ),
            requires_grad=False,
        )
        
        # 量化的B矩阵
        self.lora_b_stacked = nn.Parameter(
            torch.zeros(
                max_loras, 1, self.output_size, lora_config.max_lora_rank,
                dtype=lora_dtype
            ),
            requires_grad=False,
        )
        
        # 量化缩放因子
        if lora_dtype in [torch.int8, torch.float8_e4m3fn]:
            self.lora_a_scales = nn.Parameter(
                torch.ones(max_loras, 1, lora_config.max_lora_rank, 1),
                requires_grad=False,
            )
            self.lora_b_scales = nn.Parameter(
                torch.ones(max_loras, 1, self.output_size, 1),
                requires_grad=False,
            )
    
    def quantize_lora_weights(self, 
                             lora_a: torch.Tensor, 
                             lora_b: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """量化LoRA权重"""
        
        if isinstance(self.quant_config, Fp8Config):
            # FP8量化
            a_quantized, a_scale = self._quantize_fp8(lora_a)
            b_quantized, b_scale = self._quantize_fp8(lora_b)
        else:
            # INT8量化
            a_quantized, a_scale = self._quantize_int8(lora_a)
            b_quantized, b_scale = self._quantize_int8(lora_b)
        
        return a_quantized, b_quantized, a_scale, b_scale
    
    def _quantize_fp8(self, tensor: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """FP8量化"""
        amax = torch.max(torch.abs(tensor), dim=-1, keepdim=True)[0]
        scale = amax / torch.finfo(torch.float8_e4m3fn).max
        scale = scale.clamp(min=torch.finfo(torch.float8_e4m3fn).tiny)
        
        quantized = (tensor / scale).to(torch.float8_e4m3fn)
        return quantized, scale
    
    def _quantize_int8(self, tensor: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """INT8量化"""
        amax = torch.max(torch.abs(tensor), dim=-1, keepdim=True)[0]
        scale = amax / 127.0
        scale = scale.clamp(min=1e-8)
        
        quantized = torch.round(tensor / scale).clamp(-128, 127).to(torch.int8)
        return quantized, scale
    
    def set_lora(self, index: int, lora_a: torch.Tensor, lora_b: torch.Tensor):
        """设置量化的LoRA权重"""
        
        # 量化权重
        a_quantized, b_quantized, a_scale, b_scale = self.quantize_lora_weights(lora_a, lora_b)
        
        # 存储量化权重
        self.lora_a_stacked.data[index, 0, :lora_a.shape[-2], :lora_a.shape[-1]] = a_quantized
        self.lora_b_stacked.data[index, 0, :lora_b.shape[-2], :lora_b.shape[-1]] = b_quantized
        
        # 存储缩放因子
        if self.lora_a_scales is not None:
            self.lora_a_scales.data[index, 0, :lora_a.shape[-2], :] = a_scale
            self.lora_b_scales.data[index, 0, :lora_b.shape[-2], :] = b_scale
    
    def _apply_lora_linear(self, input_: torch.Tensor, lora_mapping: LoRAMapping) -> torch.Tensor:
        """应用量化的LoRA"""
        
        indices = lora_mapping.index_mapping
        if not indices:
            return torch.zeros(input_.shape[0], self.output_size, dtype=input_.dtype, device=input_.device)
        
        # 获取量化权重和缩放因子
        lora_a_current = self.lora_a_stacked[indices]
        lora_b_current = self.lora_b_stacked[indices]
        
        # 反量化
        if self.lora_a_scales is not None:
            a_scales = self.lora_a_scales[indices]
            b_scales = self.lora_b_scales[indices]
            
            lora_a_dequant = lora_a_current.to(torch.float16) * a_scales
            lora_b_dequant = lora_b_current.to(torch.float16) * b_scales
        else:
            lora_a_dequant = lora_a_current.to(torch.float16)
            lora_b_dequant = lora_b_current.to(torch.float16)
        
        # LoRA计算
        input_expanded = input_.unsqueeze(1).unsqueeze(1)
        intermediate = torch.matmul(input_expanded, lora_a_dequant.transpose(-2, -1))
        lora_output = torch.matmul(intermediate, lora_b_dequant.transpose(-2, -1))
        
        return lora_output.squeeze(1).squeeze(1)
```

### 4. 多LoRA管理机制

#### 4.1 LoRA工作器管理

```python
# 来源: vllm/lora/worker_manager.py
class LoRAWorkerManager:
    """LoRA工作器管理器"""
    
    def __init__(self,
                 max_num_seqs: int,
                 max_num_batched_tokens: int,
                 vocab_size: int,
                 lora_config: LoRAConfig,
                 device: torch.device,
                 embedding_modules: Dict[str, str],
                 embedding_padding_modules: List[str],
                 lora_modules: List[str]):
        
        self.max_num_seqs = max_num_seqs
        self.max_num_batched_tokens = max_num_batched_tokens
        self.lora_config = lora_config
        self.device = device
        
        # LoRA模块映射
        self.embedding_modules = embedding_modules
        self.embedding_padding_modules = embedding_padding_modules
        self.lora_modules = lora_modules
        
        # 活跃的LoRA适配器
        self.active_loras: Dict[int, LoRAModel] = {}
        
        # LoRA槽位管理
        self.lora_slots: List[Optional[int]] = [None] * lora_config.max_loras
        self.lora_id_to_index: Dict[int, int] = {}
    
    def add_lora(self, lora_request: LoRARequest) -> bool:
        """添加LoRA适配器"""
        
        if lora_request.lora_int_id in self.active_loras:
            return True  # 已存在
        
        # 查找空闲槽位
        free_slot = None
        for i, slot in enumerate(self.lora_slots):
            if slot is None:
                free_slot = i
                break
        
        if free_slot is None:
            return False  # 没有空闲槽位
        
        # 加载LoRA模型
        try:
            lora_model = self._load_lora_model(lora_request)
            
            # 分配槽位
            self.lora_slots[free_slot] = lora_request.lora_int_id
            self.lora_id_to_index[lora_request.lora_int_id] = free_slot
            self.active_loras[lora_request.lora_int_id] = lora_model
            
            # 设置模型权重
            self._set_lora_weights(free_slot, lora_model)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load LoRA {lora_request.lora_name}: {e}")
            return False
    
    def remove_lora(self, lora_id: int) -> bool:
        """移除LoRA适配器"""
        
        if lora_id not in self.active_loras:
            return False
        
        # 获取槽位索引
        slot_index = self.lora_id_to_index[lora_id]
        
        # 清理权重
        self._reset_lora_weights(slot_index)
        
        # 释放槽位
        self.lora_slots[slot_index] = None
        del self.lora_id_to_index[lora_id]
        del self.active_loras[lora_id]
        
        return True
    
    def list_loras(self) -> Dict[int, LoRAModel]:
        """列出所有活跃的LoRA适配器"""
        return self.active_loras.copy()
```

## 实践任务

### 任务1: LoRA基础实现

创建 `lora_implementation.py`:

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, List

class LoRALayer(nn.Module):
    """基础LoRA层实现"""
    
    def __init__(self, 
                 in_features: int, 
                 out_features: int, 
                 rank: int = 16,
                 alpha: float = 16.0,
                 dropout: float = 0.0):
        super().__init__()
        
        self.rank = rank
        self.alpha = alpha
        self.scaling = alpha / rank
        
        # LoRA权重
        self.lora_A = nn.Parameter(torch.randn(rank, in_features) * 0.01)
        self.lora_B = nn.Parameter(torch.zeros(out_features, rank))
        
        # Dropout
        self.dropout = nn.Dropout(dropout) if dropout > 0 else nn.Identity()
        
        # 冻结标志
        self.merged = False
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """LoRA前向传播"""
        if not self.merged:
            # 计算LoRA输出: x @ A^T @ B^T * scaling
            lora_output = self.dropout(x) @ self.lora_A.T @ self.lora_B.T * self.scaling
            return lora_output
        else:
            return torch.zeros_like(x @ self.lora_A.T @ self.lora_B.T)

class LoRALinear(nn.Module):
    """LoRA增强的线性层"""
    
    def __init__(self, 
                 base_layer: nn.Linear,
                 rank: int = 16,
                 alpha: float = 16.0,
                 dropout: float = 0.0):
        super().__init__()
        
        self.base_layer = base_layer
        self.lora = LoRALayer(
            base_layer.in_features,
            base_layer.out_features,
            rank, alpha, dropout
        )
        
        # 冻结基础层
        for param in self.base_layer.parameters():
            param.requires_grad = False
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        base_output = self.base_layer(x)
        lora_output = self.lora(x)
        return base_output + lora_output
    
    def merge_weights(self):
        """合并LoRA权重到基础层"""
        if not self.lora.merged:
            # 计算LoRA权重: B @ A * scaling
            lora_weight = self.lora.lora_B @ self.lora.lora_A * self.lora.scaling
            
            # 合并到基础权重
            self.base_layer.weight.data += lora_weight
            self.lora.merged = True
    
    def unmerge_weights(self):
        """分离LoRA权重"""
        if self.lora.merged:
            lora_weight = self.lora.lora_B @ self.lora.lora_A * self.lora.scaling
            self.base_layer.weight.data -= lora_weight
            self.lora.merged = False

class MultiLoRAManager:
    """多LoRA管理器"""
    
    def __init__(self, max_loras: int = 8):
        self.max_loras = max_loras
        self.lora_adapters: Dict[str, LoRALinear] = {}
        self.active_loras: List[str] = []
    
    def add_lora(self, name: str, lora_layer: LoRALinear) -> bool:
        """添加LoRA适配器"""
        if len(self.lora_adapters) >= self.max_loras:
            return False
        
        self.lora_adapters[name] = lora_layer
        return True
    
    def remove_lora(self, name: str) -> bool:
        """移除LoRA适配器"""
        if name in self.lora_adapters:
            del self.lora_adapters[name]
            if name in self.active_loras:
                self.active_loras.remove(name)
            return True
        return False
    
    def activate_lora(self, name: str) -> bool:
        """激活LoRA适配器"""
        if name in self.lora_adapters and name not in self.active_loras:
            self.active_loras.append(name)
            return True
        return False
    
    def deactivate_lora(self, name: str) -> bool:
        """停用LoRA适配器"""
        if name in self.active_loras:
            self.active_loras.remove(name)
            return True
        return False
    
    def forward_with_loras(self, x: torch.Tensor, base_layer: nn.Linear) -> torch.Tensor:
        """使用多个LoRA进行前向传播"""
        output = base_layer(x)
        
        for lora_name in self.active_loras:
            if lora_name in self.lora_adapters:
                lora_output = self.lora_adapters[lora_name].lora(x)
                output = output + lora_output
        
        return output

def test_lora_implementation():
    """测试LoRA实现"""
    
    # 创建基础线性层
    base_layer = nn.Linear(512, 256)
    
    # 创建LoRA层
    lora_layer = LoRALinear(base_layer, rank=16, alpha=16.0)
    
    # 测试数据
    x = torch.randn(32, 512)
    
    print("=== LoRA实现测试 ===")
    
    # 前向传播
    output = lora_layer(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    # 测试权重合并
    print(f"\n权重合并前:")
    print(f"基础权重范围: [{base_layer.weight.min():.4f}, {base_layer.weight.max():.4f}]")
    
    lora_layer.merge_weights()
    print(f"权重合并后:")
    print(f"基础权重范围: [{base_layer.weight.min():.4f}, {base_layer.weight.max():.4f}]")
    
    # 测试多LoRA管理
    print(f"\n=== 多LoRA管理测试 ===")
    manager = MultiLoRAManager(max_loras=4)
    
    # 创建多个LoRA
    for i in range(3):
        lora = LoRALinear(nn.Linear(512, 256), rank=8, alpha=8.0)
        success = manager.add_lora(f"lora_{i}", lora)
        print(f"添加 lora_{i}: {'成功' if success else '失败'}")
    
    # 激活LoRA
    manager.activate_lora("lora_0")
    manager.activate_lora("lora_1")
    print(f"激活的LoRA: {manager.active_loras}")
    
    # 使用多LoRA前向传播
    multi_output = manager.forward_with_loras(x, base_layer)
    print(f"多LoRA输出形状: {multi_output.shape}")

if __name__ == "__main__":
    test_lora_implementation()
```

### 任务2: LoRA量化实现

创建 `quantized_lora.py`:

```python
import torch
import torch.nn as nn
from typing import Tuple, Optional

class QuantizedLoRA:
    """量化LoRA实现"""
    
    def __init__(self, quantization_type: str = "int8"):
        self.quantization_type = quantization_type
    
    def quantize_weights(self, lora_A: torch.Tensor, lora_B: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """量化LoRA权重"""
        
        if self.quantization_type == "int8":
            return self._quantize_int8(lora_A, lora_B)
        elif self.quantization_type == "fp8":
            return self._quantize_fp8(lora_A, lora_B)
        else:
            raise ValueError(f"Unsupported quantization type: {self.quantization_type}")
    
    def _quantize_int8(self, lora_A: torch.Tensor, lora_B: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """INT8量化"""
        
        # 量化A矩阵
        A_amax = torch.max(torch.abs(lora_A))
        A_scale = A_amax / 127.0
        A_quantized = torch.round(lora_A / A_scale).clamp(-128, 127).to(torch.int8)
        
        # 量化B矩阵
        B_amax = torch.max(torch.abs(lora_B))
        B_scale = B_amax / 127.0
        B_quantized = torch.round(lora_B / B_scale).clamp(-128, 127).to(torch.int8)
        
        return A_quantized, B_quantized, A_scale, B_scale
    
    def _quantize_fp8(self, lora_A: torch.Tensor, lora_B: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """FP8量化"""
        
        # 量化A矩阵
        A_amax = torch.max(torch.abs(lora_A))
        A_scale = A_amax / 448.0  # FP8 E4M3最大值
        A_quantized = (lora_A / A_scale).to(torch.float8_e4m3fn)
        
        # 量化B矩阵
        B_amax = torch.max(torch.abs(lora_B))
        B_scale = B_amax / 448.0
        B_quantized = (lora_B / B_scale).to(torch.float8_e4m3fn)
        
        return A_quantized, B_quantized, A_scale, B_scale
    
    def dequantize_and_compute(self, 
                              x: torch.Tensor,
                              A_quantized: torch.Tensor,
                              B_quantized: torch.Tensor,
                              A_scale: torch.Tensor,
                              B_scale: torch.Tensor,
                              scaling: float) -> torch.Tensor:
        """反量化并计算LoRA输出"""
        
        # 反量化
        A_dequantized = A_quantized.to(torch.float16) * A_scale
        B_dequantized = B_quantized.to(torch.float16) * B_scale
        
        # 计算LoRA输出
        intermediate = x @ A_dequantized.T
        output = intermediate @ B_dequantized.T * scaling
        
        return output

class QuantizedLoRALinear(nn.Module):
    """量化LoRA线性层"""
    
    def __init__(self,
                 base_layer: nn.Linear,
                 rank: int = 16,
                 alpha: float = 16.0,
                 quantization_type: str = "int8"):
        super().__init__()
        
        self.base_layer = base_layer
        self.rank = rank
        self.alpha = alpha
        self.scaling = alpha / rank
        self.quantization_type = quantization_type
        
        # 量化器
        self.quantizer = QuantizedLoRA(quantization_type)
        
        # 量化权重存储
        self.register_buffer('lora_A_quantized', None)
        self.register_buffer('lora_B_quantized', None)
        self.register_buffer('lora_A_scale', None)
        self.register_buffer('lora_B_scale', None)
        
        # 初始化LoRA权重
        self._init_lora_weights()
    
    def _init_lora_weights(self):
        """初始化LoRA权重"""
        
        # 创建FP16权重
        lora_A = torch.randn(self.rank, self.base_layer.in_features) * 0.01
        lora_B = torch.zeros(self.base_layer.out_features, self.rank)
        
        # 量化权重
        A_q, B_q, A_s, B_s = self.quantizer.quantize_weights(lora_A, lora_B)
        
        # 存储量化权重
        self.lora_A_quantized = A_q
        self.lora_B_quantized = B_q
        self.lora_A_scale = A_s
        self.lora_B_scale = B_s
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        
        # 基础层输出
        base_output = self.base_layer(x)
        
        # 量化LoRA输出
        lora_output = self.quantizer.dequantize_and_compute(
            x, 
            self.lora_A_quantized,
            self.lora_B_quantized,
            self.lora_A_scale,
            self.lora_B_scale,
            self.scaling
        )
        
        return base_output + lora_output
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        
        # 计算原始FP16权重大小
        fp16_size = (self.rank * self.base_layer.in_features + 
                    self.base_layer.out_features * self.rank) * 2  # 2 bytes per FP16
        
        # 计算量化权重大小
        if self.quantization_type == "int8":
            quant_size = (self.rank * self.base_layer.in_features + 
                         self.base_layer.out_features * self.rank) * 1  # 1 byte per INT8
            scale_size = 2 * 4  # 2 scales * 4 bytes per FP32
        elif self.quantization_type == "fp8":
            quant_size = (self.rank * self.base_layer.in_features + 
                         self.base_layer.out_features * self.rank) * 1  # 1 byte per FP8
            scale_size = 2 * 2  # 2 scales * 2 bytes per FP16
        
        total_quant_size = quant_size + scale_size
        
        return {
            "fp16_size_mb": fp16_size / 1024 / 1024,
            "quantized_size_mb": total_quant_size / 1024 / 1024,
            "compression_ratio": fp16_size / total_quant_size
        }

def test_quantized_lora():
    """测试量化LoRA"""
    
    print("=== 量化LoRA测试 ===")
    
    # 创建基础层
    base_layer = nn.Linear(1024, 512)
    
    # 创建量化LoRA层
    lora_int8 = QuantizedLoRALinear(base_layer, rank=32, quantization_type="int8")
    lora_fp8 = QuantizedLoRALinear(base_layer, rank=32, quantization_type="fp8")
    
    # 测试数据
    x = torch.randn(64, 1024, dtype=torch.float16)
    
    # 前向传播测试
    output_int8 = lora_int8(x)
    output_fp8 = lora_fp8(x)
    
    print(f"输入形状: {x.shape}")
    print(f"INT8 LoRA输出形状: {output_int8.shape}")
    print(f"FP8 LoRA输出形状: {output_fp8.shape}")
    
    # 内存使用对比
    print(f"\n=== 内存使用对比 ===")
    
    int8_memory = lora_int8.get_memory_usage()
    fp8_memory = lora_fp8.get_memory_usage()
    
    print(f"INT8量化:")
    print(f"  FP16大小: {int8_memory['fp16_size_mb']:.2f} MB")
    print(f"  量化大小: {int8_memory['quantized_size_mb']:.2f} MB")
    print(f"  压缩比: {int8_memory['compression_ratio']:.1f}x")
    
    print(f"FP8量化:")
    print(f"  FP16大小: {fp8_memory['fp16_size_mb']:.2f} MB")
    print(f"  量化大小: {fp8_memory['quantized_size_mb']:.2f} MB")
    print(f"  压缩比: {fp8_memory['compression_ratio']:.1f}x")
    
    # 精度对比
    base_output = base_layer(x)
    
    int8_error = torch.mean(torch.abs(output_int8 - base_output))
    fp8_error = torch.mean(torch.abs(output_fp8 - base_output))
    
    print(f"\n=== 精度对比 ===")
    print(f"INT8 LoRA误差: {int8_error:.6f}")
    print(f"FP8 LoRA误差: {fp8_error:.6f}")

if __name__ == "__main__":
    test_quantized_lora()
```

## 学习检查点

### 第1天结束检查 (9.16)
- [ ] 理解LoRA的数学原理和低秩分解概念
- [ ] 掌握vLLM中LoRA的架构设计
- [ ] 理解LoRA层的实现机制
- [ ] 完成基础LoRA实现

### 第2天结束检查 (9.17)
- [ ] 掌握LoRA权重量化策略
- [ ] 理解多LoRA管理机制
- [ ] 完成量化LoRA实现
- [ ] 能够分析LoRA的内存和精度权衡

## 参考资料

### vLLM核心源码文件
1. `vllm/lora/` - LoRA实现目录
2. `vllm/lora/layers.py` - LoRA层实现
3. `vllm/lora/worker_manager.py` - LoRA工作器管理
4. `vllm/config.py` - LoRA配置

### 技术论文
1. "LoRA: Low-Rank Adaptation of Large Language Models"
2. "QLoRA: Efficient Finetuning of Quantized LLMs"
3. "AdaLoRA: Adaptive Budget Allocation for Parameter-Efficient Fine-Tuning"

## 下一步预告

完成LoRA量化学习后，下一步将学习LLMCompressor与vLLM的集成，重点关注：
- LLMCompressor的量化流程
- 模型格式转换和兼容性
- 端到端的量化工作流
- 性能优化和部署策略
