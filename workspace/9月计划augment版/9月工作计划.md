1. 量化从int8 fp8 int4参数维度进行分析分类 （9.1-9.5）
  1. 学术相关方向 了解 
  2. 工程化相关应用作为主要研究方向(针对vllm有实现的需要单独梳理实现流程）优先级高 
2. vllm框架相关文档学习，有疑问可以与元巍交流 优先级高 （9.1-9.5 目前基础的框架相关概念已经清晰）
3. 量化关于kvcache工程化实现，尤其是vllm框架是否有使用，如果有，采用的方式方法以及实现原理和实现流程 优先级高  (9.8-9.12 vllm/model_executor/layers/quantization/kv_cache.py)
4. vllm相关量化算子（1. Quant 2.layernm等算子的融合算子 3.gemm算子 优先级高 (9.8-9.12)
5. 量化的稀疏与压缩方向 了解 （9.15-9.19）
6. 量化中lora相关量化在vllm中的实现 优先级高 (9.15-9.19)
7. 针对llm-compressor中与vllm中量化相关联的功能进行熟悉与匹配 例如各种量化功能的实现等 优先级高 （9.15-9.19）
8. 量化中关于moe量化的方向，在vllm中是否有实现和使用，若有，进一步梳理原理和实现流程 优先级中 (9.22-9.26)
9. 量化中通信相关的量化实现，int8权重与fp16激活参数的通信 了解 （9.22-9.26）
10. zx相关的vllm推理实现和后续端到端量化推理实现与测试 后续重点工作 (10月+）