# 稀疏与压缩技术学习文档

## 学习时间：9.15 (1天)

## 学习目标

1. 理解结构化稀疏与非结构化稀疏的区别和应用
2. 掌握压缩存储格式和相关算法
3. 学习稀疏矩阵乘法的优化策略
4. 了解vLLM中的稀疏支持机制
5. 理解稀疏化与量化的结合使用

## 稀疏化基础理论

### 1. 稀疏化概念与分类

#### 1.1 稀疏化的动机
- **模型压缩**: 减少参数数量，降低存储需求
- **计算加速**: 跳过零值计算，提高推理速度
- **内存优化**: 减少内存访问，提高缓存效率
- **能耗降低**: 减少计算量，降低功耗

#### 1.2 稀疏化分类

**按结构分类**:
```
稀疏化类型
├── 非结构化稀疏 (Unstructured Sparsity)
│   ├── 随机稀疏
│   ├── 幅度剪枝
│   └── 梯度剪枝
├── 结构化稀疏 (Structured Sparsity)
│   ├── N:M稀疏 (如2:4稀疏)
│   ├── 块稀疏 (Block Sparsity)
│   └── 通道稀疏 (Channel Sparsity)
└── 半结构化稀疏 (Semi-structured Sparsity)
    ├── 2:4稀疏
    └── 4:8稀疏
```

### 2. 压缩存储格式

#### 2.1 CSR (Compressed Sparse Row) 格式

```python
# CSR格式示例
class CSRMatrix:
    """CSR稀疏矩阵格式"""
    
    def __init__(self, data, indices, indptr, shape):
        self.data = data        # 非零元素值
        self.indices = indices  # 列索引
        self.indptr = indptr    # 行指针
        self.shape = shape      # 矩阵形状
    
    @classmethod
    def from_dense(cls, dense_matrix):
        """从稠密矩阵转换为CSR格式"""
        rows, cols = dense_matrix.shape
        data = []
        indices = []
        indptr = [0]
        
        for i in range(rows):
            row_start = len(data)
            for j in range(cols):
                if dense_matrix[i, j] != 0:
                    data.append(dense_matrix[i, j])
                    indices.append(j)
            indptr.append(len(data))
        
        return cls(
            data=torch.tensor(data),
            indices=torch.tensor(indices),
            indptr=torch.tensor(indptr),
            shape=(rows, cols)
        )
    
    def to_dense(self):
        """转换回稠密矩阵"""
        dense = torch.zeros(self.shape)
        for i in range(self.shape[0]):
            start = self.indptr[i]
            end = self.indptr[i + 1]
            for j in range(start, end):
                col = self.indices[j]
                dense[i, col] = self.data[j]
        return dense
```

#### 2.2 N:M稀疏格式

```python
# 来源: vllm/model_executor/layers/quantization/compressed_tensors/schemes/
class NMSparsity:
    """N:M稀疏格式实现"""
    
    def __init__(self, n: int, m: int):
        self.n = n  # 每M个元素中保留N个
        self.m = m
        
    def apply_sparsity(self, weight: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """应用N:M稀疏化"""
        
        # 重塑权重为(*, M)形状
        original_shape = weight.shape
        weight_reshaped = weight.view(-1, self.m)
        
        # 为每个M元素组选择top-N
        _, indices = torch.topk(torch.abs(weight_reshaped), self.n, dim=1)
        
        # 创建掩码
        mask = torch.zeros_like(weight_reshaped)
        mask.scatter_(1, indices, 1)
        
        # 应用稀疏化
        sparse_weight = weight_reshaped * mask
        
        # 重塑回原始形状
        sparse_weight = sparse_weight.view(original_shape)
        mask = mask.view(original_shape)
        
        return sparse_weight, mask
    
    def compress_24(self, weight: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """2:4稀疏压缩存储"""
        
        # 重塑为(*, 4)形状
        weight_reshaped = weight.view(-1, 4)
        mask_reshaped = mask.view(-1, 4)
        
        compressed_data = []
        metadata = []
        
        for i in range(weight_reshaped.shape[0]):
            row = weight_reshaped[i]
            row_mask = mask_reshaped[i]
            
            # 提取非零元素
            nonzero_indices = torch.nonzero(row_mask).squeeze()
            nonzero_values = row[nonzero_indices]
            
            # 存储值和位置信息
            compressed_data.append(nonzero_values)
            
            # 编码位置信息（2:4稀疏有6种可能的模式）
            if len(nonzero_indices) == 2:
                if torch.equal(nonzero_indices, torch.tensor([0, 1])):
                    metadata.append(0)  # 0b00
                elif torch.equal(nonzero_indices, torch.tensor([0, 2])):
                    metadata.append(1)  # 0b01
                elif torch.equal(nonzero_indices, torch.tensor([0, 3])):
                    metadata.append(2)  # 0b10
                elif torch.equal(nonzero_indices, torch.tensor([1, 2])):
                    metadata.append(3)  # 0b11
                elif torch.equal(nonzero_indices, torch.tensor([1, 3])):
                    metadata.append(4)  # 0b100
                elif torch.equal(nonzero_indices, torch.tensor([2, 3])):
                    metadata.append(5)  # 0b101
        
        return torch.cat(compressed_data), torch.tensor(metadata)
```

### 3. vLLM中的稀疏支持

#### 3.1 Compressed Tensors集成

```python
# 来源: vllm/model_executor/layers/quantization/compressed_tensors/compressed_tensors.py
class CompressedTensorsConfig(QuantizationConfig):
    """Compressed Tensors量化配置"""
    
    def __init__(self,
                 target_scheme: str,
                 quantization_format: str,
                 global_compression_ratio: Optional[float] = None,
                 sparsity_config: Optional[Dict] = None):
        
        self.target_scheme = target_scheme
        self.quantization_format = quantization_format
        self.global_compression_ratio = global_compression_ratio
        self.sparsity_config = sparsity_config or {}
        
        # 解析稀疏配置
        if "sparsity" in self.sparsity_config:
            self.sparsity_structure = self.sparsity_config["sparsity"].get("structure", "unstructured")
            self.sparsity_ratio = self.sparsity_config["sparsity"].get("sparsity", 0.0)
    
    def get_supported_act_dtypes(self) -> Set[torch.dtype]:
        return {torch.float16, torch.bfloat16}
    
    def get_min_capability(self) -> int:
        return 75  # 支持稀疏的最小GPU架构

class CompressedTensorsLinearMethod(LinearMethodBase):
    """Compressed Tensors线性层方法"""
    
    def __init__(self, quant_config: CompressedTensorsConfig):
        self.quant_config = quant_config
        
    def create_weights(self, layer: torch.nn.Module, **kwargs):
        """创建压缩权重"""
        
        # 根据稀疏配置创建不同的权重格式
        if hasattr(self.quant_config, 'sparsity_structure'):
            if self.quant_config.sparsity_structure == "2:4":
                self._create_24_sparse_weights(layer, **kwargs)
            elif self.quant_config.sparsity_structure == "unstructured":
                self._create_unstructured_sparse_weights(layer, **kwargs)
            else:
                self._create_dense_weights(layer, **kwargs)
        else:
            self._create_dense_weights(layer, **kwargs)
    
    def _create_24_sparse_weights(self, layer: torch.nn.Module, **kwargs):
        """创建2:4稀疏权重"""
        
        input_size = kwargs["input_size_per_partition"]
        output_size = kwargs["output_size"]
        
        # 压缩后的权重大小（2:4稀疏压缩比为50%）
        compressed_size = input_size * output_size // 2
        
        # 压缩权重
        compressed_weight = ModelWeightParameter(
            data=torch.empty(compressed_size, dtype=kwargs["params_dtype"]),
            weight_loader=self._weight_loader_24,
        )
        
        # 元数据（存储稀疏模式）
        metadata = ModelWeightParameter(
            data=torch.empty(input_size * output_size // 4, dtype=torch.uint8),
            weight_loader=self._metadata_loader_24,
        )
        
        layer.register_parameter("compressed_weight", compressed_weight)
        layer.register_parameter("metadata", metadata)
    
    def apply(self, layer: torch.nn.Module, x: torch.Tensor, bias: Optional[torch.Tensor] = None) -> torch.Tensor:
        """应用稀疏线性变换"""
        
        if hasattr(layer, "compressed_weight"):
            # 使用稀疏GEMM
            output = self._sparse_gemm(x, layer.compressed_weight, layer.metadata)
        else:
            # 标准GEMM
            output = torch.matmul(x, layer.weight.t())
        
        if bias is not None:
            output = output + bias
            
        return output
    
    def _sparse_gemm(self, input: torch.Tensor, compressed_weight: torch.Tensor, metadata: torch.Tensor) -> torch.Tensor:
        """稀疏GEMM实现"""
        
        # 调用CUTLASS稀疏GEMM kernel
        return torch.ops._C.cutlass_sparse_gemm(
            input,
            compressed_weight,
            metadata,
            "2:4"  # 稀疏模式
        )
```

#### 3.2 稀疏注意力实现

```python
# 稀疏注意力实现示例
class SparseAttention(nn.Module):
    """稀疏注意力层"""
    
    def __init__(self, d_model: int, num_heads: int, sparsity_pattern: str = "local"):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        self.sparsity_pattern = sparsity_pattern
        
        # 线性投影层（可以应用权重稀疏化）
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.o_proj = nn.Linear(d_model, d_model)
        
    def create_sparse_mask(self, seq_len: int) -> torch.Tensor:
        """创建稀疏注意力掩码"""
        
        if self.sparsity_pattern == "local":
            # 局部注意力：每个位置只关注周围的位置
            window_size = 64
            mask = torch.zeros(seq_len, seq_len, dtype=torch.bool)
            
            for i in range(seq_len):
                start = max(0, i - window_size // 2)
                end = min(seq_len, i + window_size // 2 + 1)
                mask[i, start:end] = True
                
        elif self.sparsity_pattern == "strided":
            # 步长注意力：每隔固定步长关注一个位置
            stride = 8
            mask = torch.zeros(seq_len, seq_len, dtype=torch.bool)
            
            for i in range(seq_len):
                # 局部窗口
                local_start = max(0, i - 32)
                local_end = min(seq_len, i + 32)
                mask[i, local_start:local_end] = True
                
                # 步长位置
                for j in range(0, seq_len, stride):
                    mask[i, j] = True
                    
        elif self.sparsity_pattern == "random":
            # 随机稀疏：随机选择一定比例的位置
            sparsity_ratio = 0.1  # 保留10%的连接
            mask = torch.rand(seq_len, seq_len) < sparsity_ratio
            
            # 确保对角线（自注意力）始终存在
            mask.fill_diagonal_(True)
        
        return mask
    
    def forward(self, x: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """稀疏注意力前向传播"""
        
        batch_size, seq_len, _ = x.shape
        
        # 计算Q, K, V
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 创建稀疏掩码
        sparse_mask = self.create_sparse_mask(seq_len).to(x.device)
        
        # 计算注意力分数
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        
        # 应用稀疏掩码
        attn_scores = attn_scores.masked_fill(~sparse_mask.unsqueeze(0).unsqueeze(0), float('-inf'))
        
        # 应用额外的注意力掩码（如果提供）
        if attention_mask is not None:
            attn_scores = attn_scores + attention_mask
        
        # Softmax
        attn_weights = F.softmax(attn_scores, dim=-1)
        
        # 应用到V
        attn_output = torch.matmul(attn_weights, v)
        
        # 重塑输出
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, seq_len, self.d_model)
        
        # 输出投影
        output = self.o_proj(attn_output)
        
        return output
```

## 实践任务

### 任务1: 稀疏化算法实现

创建 `sparsity_algorithms.py`:

```python
import torch
import torch.nn as nn
import numpy as np

class MagnitudePruning:
    """幅度剪枝算法"""
    
    def __init__(self, sparsity_ratio: float = 0.5):
        self.sparsity_ratio = sparsity_ratio
    
    def prune_unstructured(self, weight: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """非结构化剪枝"""
        
        # 计算阈值
        weight_abs = torch.abs(weight)
        threshold = torch.quantile(weight_abs, self.sparsity_ratio)
        
        # 创建掩码
        mask = weight_abs > threshold
        
        # 应用剪枝
        pruned_weight = weight * mask
        
        return pruned_weight, mask
    
    def prune_24_structured(self, weight: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """2:4结构化剪枝"""
        
        # 重塑为(..., 4)形状
        original_shape = weight.shape
        weight_reshaped = weight.view(-1, 4)
        
        # 为每个4元素组选择top-2
        _, indices = torch.topk(torch.abs(weight_reshaped), 2, dim=1)
        
        # 创建掩码
        mask = torch.zeros_like(weight_reshaped)
        mask.scatter_(1, indices, 1)
        
        # 应用剪枝
        pruned_weight = weight_reshaped * mask
        
        # 重塑回原始形状
        pruned_weight = pruned_weight.view(original_shape)
        mask = mask.view(original_shape)
        
        return pruned_weight, mask

class GradualPruning:
    """渐进式剪枝"""
    
    def __init__(self, 
                 initial_sparsity: float = 0.0,
                 final_sparsity: float = 0.9,
                 pruning_steps: int = 1000):
        self.initial_sparsity = initial_sparsity
        self.final_sparsity = final_sparsity
        self.pruning_steps = pruning_steps
        self.current_step = 0
    
    def get_current_sparsity(self) -> float:
        """计算当前步骤的稀疏度"""
        if self.current_step >= self.pruning_steps:
            return self.final_sparsity
        
        # 多项式衰减
        progress = self.current_step / self.pruning_steps
        sparsity = self.initial_sparsity + (self.final_sparsity - self.initial_sparsity) * (progress ** 3)
        
        return sparsity
    
    def step(self, weight: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """执行一步剪枝"""
        current_sparsity = self.get_current_sparsity()
        
        # 幅度剪枝
        pruner = MagnitudePruning(current_sparsity)
        pruned_weight, mask = pruner.prune_unstructured(weight)
        
        self.current_step += 1
        
        return pruned_weight, mask

def test_sparsity_algorithms():
    """测试稀疏化算法"""
    
    # 创建测试权重
    weight = torch.randn(1024, 512)
    
    print("=== 稀疏化算法测试 ===")
    print(f"原始权重形状: {weight.shape}")
    print(f"原始非零元素: {torch.count_nonzero(weight)}")
    
    # 测试非结构化剪枝
    pruner = MagnitudePruning(sparsity_ratio=0.5)
    pruned_weight, mask = pruner.prune_unstructured(weight)
    
    sparsity = 1.0 - torch.count_nonzero(pruned_weight).float() / weight.numel()
    print(f"\n非结构化剪枝:")
    print(f"目标稀疏度: 50%, 实际稀疏度: {sparsity:.1%}")
    print(f"剪枝后非零元素: {torch.count_nonzero(pruned_weight)}")
    
    # 测试2:4结构化剪枝
    weight_24 = torch.randn(256, 512)  # 确保可以被4整除
    pruned_24, mask_24 = pruner.prune_24_structured(weight_24)
    
    sparsity_24 = 1.0 - torch.count_nonzero(pruned_24).float() / weight_24.numel()
    print(f"\n2:4结构化剪枝:")
    print(f"理论稀疏度: 50%, 实际稀疏度: {sparsity_24:.1%}")
    print(f"剪枝后非零元素: {torch.count_nonzero(pruned_24)}")
    
    # 验证2:4模式
    weight_24_reshaped = pruned_24.view(-1, 4)
    valid_24_pattern = True
    for i in range(weight_24_reshaped.shape[0]):
        nonzero_count = torch.count_nonzero(weight_24_reshaped[i])
        if nonzero_count != 2:
            valid_24_pattern = False
            break
    
    print(f"2:4模式验证: {'通过' if valid_24_pattern else '失败'}")
    
    # 测试渐进式剪枝
    print(f"\n渐进式剪枝测试:")
    gradual_pruner = GradualPruning(0.0, 0.8, 10)
    
    for step in range(5):
        _, _ = gradual_pruner.step(weight)
        current_sparsity = gradual_pruner.get_current_sparsity()
        print(f"步骤 {step + 1}: 目标稀疏度 {current_sparsity:.1%}")

if __name__ == "__main__":
    test_sparsity_algorithms()
```

### 任务2: 稀疏矩阵乘法优化

创建 `sparse_gemm.py`:

```python
import torch
import time
from typing import Tuple

class SparseGEMM:
    """稀疏矩阵乘法实现"""
    
    @staticmethod
    def csr_dense_mm(csr_matrix, dense_matrix: torch.Tensor) -> torch.Tensor:
        """CSR格式稀疏矩阵与稠密矩阵相乘"""
        
        data, indices, indptr = csr_matrix.data, csr_matrix.indices, csr_matrix.indptr
        sparse_rows, sparse_cols = csr_matrix.shape
        dense_cols = dense_matrix.shape[1]
        
        # 输出矩阵
        output = torch.zeros(sparse_rows, dense_cols, dtype=dense_matrix.dtype, device=dense_matrix.device)
        
        # 逐行计算
        for i in range(sparse_rows):
            start = indptr[i]
            end = indptr[i + 1]
            
            if start < end:
                # 获取当前行的非零元素
                row_data = data[start:end]
                row_indices = indices[start:end]
                
                # 计算当前行的结果
                selected_rows = dense_matrix[row_indices]
                output[i] = torch.sum(row_data.unsqueeze(1) * selected_rows, dim=0)
        
        return output
    
    @staticmethod
    def sparse_24_mm(compressed_weight: torch.Tensor, 
                     metadata: torch.Tensor,
                     input_tensor: torch.Tensor) -> torch.Tensor:
        """2:4稀疏矩阵乘法模拟实现"""
        
        # 这里是简化的实现，实际应该使用CUTLASS等优化库
        batch_size, input_dim = input_tensor.shape
        output_dim = compressed_weight.shape[0] // 2  # 2:4压缩比为50%
        
        # 解压缩权重（简化版本）
        decompressed_weight = torch.zeros(output_dim, input_dim, dtype=compressed_weight.dtype, device=compressed_weight.device)
        
        # 根据元数据恢复稀疏模式
        compressed_idx = 0
        for i in range(output_dim):
            for j in range(0, input_dim, 4):
                if j + 3 < input_dim:
                    # 获取4元素组的元数据
                    meta_idx = (i * input_dim + j) // 4
                    if meta_idx < len(metadata):
                        pattern = metadata[meta_idx].item()
                        
                        # 根据模式恢复非零位置（简化版本）
                        if pattern == 0:  # [0, 1]
                            decompressed_weight[i, j] = compressed_weight[compressed_idx]
                            decompressed_weight[i, j + 1] = compressed_weight[compressed_idx + 1]
                        elif pattern == 1:  # [0, 2]
                            decompressed_weight[i, j] = compressed_weight[compressed_idx]
                            decompressed_weight[i, j + 2] = compressed_weight[compressed_idx + 1]
                        # ... 其他模式
                        
                        compressed_idx += 2
        
        # 执行标准矩阵乘法
        return torch.matmul(input_tensor, decompressed_weight.t())

def benchmark_sparse_gemm():
    """稀疏GEMM性能测试"""
    
    # 测试参数
    batch_size = 128
    input_dim = 4096
    output_dim = 4096
    sparsity = 0.5
    
    print("=== 稀疏GEMM性能测试 ===")
    
    # 创建测试数据
    input_tensor = torch.randn(batch_size, input_dim, dtype=torch.float16, device='cuda')
    dense_weight = torch.randn(output_dim, input_dim, dtype=torch.float16, device='cuda')
    
    # 创建稀疏权重
    weight_abs = torch.abs(dense_weight)
    threshold = torch.quantile(weight_abs, sparsity)
    sparse_mask = weight_abs > threshold
    sparse_weight = dense_weight * sparse_mask
    
    print(f"矩阵大小: {batch_size} x {input_dim} @ {output_dim} x {input_dim}")
    print(f"稀疏度: {sparsity:.1%}")
    print(f"实际稀疏度: {1.0 - torch.count_nonzero(sparse_weight).float() / sparse_weight.numel():.1%}")
    
    # 稠密GEMM基准测试
    torch.cuda.synchronize()
    start_time = time.time()
    
    for _ in range(100):
        dense_output = torch.matmul(input_tensor, dense_weight.t())
    
    torch.cuda.synchronize()
    dense_time = time.time() - start_time
    
    print(f"\n稠密GEMM时间: {dense_time:.4f}s")
    
    # 稀疏GEMM测试（使用稀疏权重但稠密计算）
    torch.cuda.synchronize()
    start_time = time.time()
    
    for _ in range(100):
        sparse_output = torch.matmul(input_tensor, sparse_weight.t())
    
    torch.cuda.synchronize()
    sparse_time = time.time() - start_time
    
    print(f"稀疏权重GEMM时间: {sparse_time:.4f}s")
    print(f"加速比: {dense_time / sparse_time:.2f}x")
    
    # 验证结果正确性
    error = torch.mean(torch.abs(sparse_output - torch.matmul(input_tensor, sparse_weight.t())))
    print(f"计算误差: {error:.6f}")
    
    # 内存使用对比
    dense_memory = dense_weight.numel() * dense_weight.element_size()
    sparse_memory = torch.count_nonzero(sparse_weight) * sparse_weight.element_size()
    
    print(f"\n内存使用:")
    print(f"稠密权重: {dense_memory / 1024 / 1024:.2f} MB")
    print(f"稀疏权重: {sparse_memory / 1024 / 1024:.2f} MB")
    print(f"内存节省: {(1 - sparse_memory / dense_memory):.1%}")

if __name__ == "__main__":
    test_sparsity_algorithms()
    benchmark_sparse_gemm()
```

## 学习检查点

### 学习结束检查 (9.15)
- [ ] 理解结构化稀疏与非结构化稀疏的区别
- [ ] 掌握CSR等压缩存储格式
- [ ] 了解N:M稀疏的实现原理
- [ ] 理解vLLM中的稀疏支持机制
- [ ] 完成稀疏化算法实现
- [ ] 完成稀疏GEMM性能测试

## 参考资料

### vLLM核心源码文件
1. `vllm/model_executor/layers/quantization/compressed_tensors/` - 压缩张量实现
2. `vllm/model_executor/layers/quantization/compressed_tensors/schemes/` - 稀疏化方案

### 技术论文
1. "The Lottery Ticket Hypothesis: Finding Sparse, Trainable Neural Networks"
2. "Magnitude-based Pruning for Sparse Neural Networks"
3. "Accelerating Sparse Deep Neural Networks"
4. "2:4 Structured Sparsity for Deep Neural Networks"

### 开源项目
1. [SparseML](https://github.com/neuralmagic/sparseml) - 神经网络稀疏化工具
2. [CUTLASS](https://github.com/NVIDIA/cutlass) - CUDA模板库，支持稀疏GEMM

## 下一步预告

完成稀疏与压缩技术学习后，下一步将深入学习LoRA量化实现，重点关注：
- LoRA的数学原理和实现细节
- 量化LoRA权重的策略
- 多LoRA适配器的管理机制
- vLLM中的LoRA集成方式
