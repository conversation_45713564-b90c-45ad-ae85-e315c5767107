
# 学习文档: LLM Compressor 与 vLLM 对比

本文档旨在对比`llm-compressor`和`vllm`这两个框架，阐明它们的定位、功能和协同工作方式。

---

## 1. 核心定位

理解两个框架最关键的一点是它们的**核心定位不同**。

*   ### **LLM Compressor: 模型的“离线优化器” (Offline Optimizer)**
    *   **目标:** 将一个训练好的、庞大的FP16/FP32模型，通过各种压缩算法，转换成一个更小、更快、更高效的模型。
    *   **角色:** 类似于一个编译器或转换器。它接收一个模型作为输入，输出一个优化后的模型。
    *   **工作模式:** 离线执行。你提供一个模型、一些校准数据和一个“配方”（Recipe），它会执行一个`one-shot`或`finetune`过程来压缩模型。

*   ### **vLLM: 模型的高性能“在线推理引擎” (Online Inference Engine)**
    *   **目标:** 以最高的吞吐量和最低的延迟来提供LLM推理服务。
    *   **角色:** 类似于一个Web服务器或数据库。它接收用户请求，并实时返回模型生成的结果。
    *   **工作模式:** 在线服务。它加载一个（可能是已经优化过的）模型，并持续处理传入的推理请求。

**一个简单的比喻:**

*   `llm-compressor` 就像一个**厨师**，他把生的食材（大模型）通过各种烹饪技巧（压缩算法）做成一道精致的菜肴（优化后的模型）。
*   `vllm` 就像一个**餐厅**，它把做好的菜肴（优化后的模型）高效地提供给顾客（用户请求）。

---

## 2. 功能与抽象对比

| 特性 | LLM Compressor | vLLM |
| :--- | :--- | :--- |
| **核心抽象** | `Modifier` | `LLMEngine`, `Scheduler`, `Worker` |
| **配置方式** | `Recipe` (YAML文件) | `EngineArgs` (Python类) |
| **主要功能** | 模型压缩算法的实现 | 高性能推理架构 |
| **量化算法** | **提供算法本身:** SmoothQuant, GPTQ, AWQ, QAT | **提供算法的推理实现:** 加载和执行已量化的模型 |
| **稀疏化** | **支持:** Wanda Pruning, Magnitude Pruning | **不支持:** vLLM目前没有对稀疏模型的原生优化 |
| **动态性** | **低:** 离线处理，一次性生成模型 | **高:** 支持Continuous Batching, 动态LoRA加载 |
| **主要产出** | 优化后的模型文件 (e.g., `pytorch_model.bin`, `quant_config.json`) | 推理结果 (生成的文本) |

---

## 3. 源码结构对比

### LLM Compressor (`llmcompressor/`)

*   `modifiers/`: **框架的核心**。每个子目录都是一种压缩算法的实现。
    *   `quantization/`: 包含`GPTQModifier`, `QuantizationModifier`等。
    *   `smoothquant/`: `SmoothQuantModifier`的实现。
    *   `pruning/`: 包含`Wanda`和`Magnitude`剪枝。
*   `recipe/`: 定义了如何解析和应用`Recipe`文件。`Recipe`是所有`Modifier`的集合。
*   `entrypoints/`: 提供了执行压缩的入口脚本。
    *   `oneshot.py`: 用于执行PTQ（训练后量化）等一次性压缩任务。
    *   `train.py`: 用于执行QAT（量化感知训练）等需要微调的压缩任务。
*   `pipelines/`: 定义了`Modifier`的应用顺序和逻辑，例如`LayerSequentialPipeline`。

### vLLM (`vllm/`)

*   `engine/`: **框架的入口和控制中心**。
    *   `llm_engine.py`: `LLMEngine`类，是用户交互的主要接口。
*   `core/`: **调度和内存管理的核心**。
    *   `scheduler.py`: 实现Continuous Batching。
    *   `cache_engine.py`: 实现PagedAttention的物理内存管理。
*   `worker/`: **模型执行单元**。
    *   `worker.py`: `Worker`类，负责在单个GPU上执行模型前向传播。
*   `model_executor/`: **模型加载和执行的细节**。
    *   `layers/quantization/`: **量化推理的核心**。包含了各种`QuantizationMethod`，用于加载和执行不同格式的量化模型。
    *   `models/`: 各种模型架构的实现（如`llama.py`），在这里会调用Attention和量化层。
*   `attention/`: PagedAttention的CUDA/Triton Kernel实现。

---

## 4. 协同工作流程 (End-to-End Workflow)

`llm-compressor`和`vllm`可以完美地协同工作，形成一个完整的“模型优化-部署”流水线。

**步骤 1: 使用 LLM Compressor 优化模型**

1.  **准备环境:**
    *   一个FP16格式的Hugging Face模型。
    *   少量校准数据（几百个样本即可）。
    *   一个`recipe.yaml`文件。

2.  **创建`recipe.yaml`:**
    ```yaml
    # example_recipe.yaml
    test_stage:
        obcq_modifiers:
            SparseGPTModifier:
                sparsity: 0.5
                block_size: 128
                targets: ["LlamaDecoderLayer"]

    quant_stage:
        quant_modifiers:
            QuantizationModifier:
                ignore: ["lm_head"]
                config_groups:
                    group_1:
                        weights:
                            num_bits: 8
                            type: "int"
                            symmetric: True
                        input_activations: null
                targets: ["Linear"]
    ```
    *这个配方定义了先做剪枝（SparseGPT），然后做INT8量化。*

3.  **执行`oneshot`脚本:**
    ```bash
    llmcompressor.oneshot \
        --model-path /path/to/fp16/model \
        --dataset-path /path/to/calibration/data \
        --recipe-path example_recipe.yaml \
        --output-dir /path/to/compressed/model \
        --num-samples 128
    ```

4.  **产出:**
    *   在`/path/to/compressed/model`目录下，你会得到优化后的模型文件，包括权重、配置文件以及一个`quantization_config.json`（如果应用了量化）。

**步骤 2: 使用 vLLM 部署优化后的模型**

1.  **编写Python部署脚本:**
    ```python
    from vllm import LLMEngine, EngineArgs, SamplingParams

    # 1. 定义引擎参数，指向压缩后的模型
    model_path = "/path/to/compressed/model"
    engine_args = EngineArgs(model=model_path)

    # 2. 创建LLMEngine
    # vLLM会自动检测quantization_config.json并加载相应的量化方法
    llm_engine = LLMEngine.from_engine_args(engine_args)

    # 3. 定义采样参数和prompt
    sampling_params = SamplingParams(temperature=0.7, top_p=0.95, max_tokens=100)
    prompt = "The capital of France is"

    # 4. 添加请求并获取输出
    request_id = "my-request-1"
    llm_engine.add_request(request_id, prompt, sampling_params)

    while True:
        request_outputs = llm_engine.step()
        for output in request_outputs:
            if output.finished:
                print(output.outputs[0].text)
                # ... 退出循环
        # ...
    ```

2.  **执行:**
    *   vLLM启动时，`UnquantizedLinearMethod`或`AWQLinearMethod`等会根据`quantization_config.json`的配置，将模型中的`nn.Linear`层替换为高性能的量化层。
    *   引擎开始服务，所有推理都在优化后的模型上进行。

---

## 5. 总结

*   **不要混淆它们的角色:** `llm-compressor`是**生产者**，生产优化模型；`vllm`是**消费者**，消费模型来提供服务。
*   **关注点分离:** `llm-compressor`关注压缩算法的数学原理和实现；`vllm`关注系统工程，如内存管理、调度和CUDA优化。
*   **强大的组合:** 两者结合，提供了一个从模型优化到高性能部署的端到端工业级解决方案。
