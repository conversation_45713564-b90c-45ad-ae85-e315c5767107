# vLLM KV Cache 量化方案预研

**预研周期:** 9.2 ~ 9.4
**核心目标:** 深入分析 vLLM 中 KV Cache 量化的实现原理、性能权衡以及与 vLLM 整体架构的结合方式。

---

## 1. 背景：为何要量化 KV Cache？

在大型语言模型（LLM）的推理过程中，显存占用主要来自两部分：模型权重和 KV Cache。对于长序列或大批量（Batch Size）的场景，KV Cache 的显存占用甚至会超过模型权重，成为服务部署的主要瓶颈。

*   **显存瓶颈:**
    *   **计算公式:** `显存 (GB) ≈ 2 * (num_layers * hidden_size * num_tokens) * bytes_per_element / 1024^3`
    *   随着上下文长度（`num_tokens`）的增加，KV Cache 的大小呈线性增长。
*   **带宽瓶颈:**
    *   在生成每个新 Token 时，模型需要从高带宽内存（HBM）中读取完整的 KV Cache 来计算注意力。巨大的 Cache 尺寸会消耗大量内存带宽，限制推理速度。

**KV Cache 量化**通过将 KV Cache 的数据类型从 FP16/BF16 转换为低比特格式（如 INT8），旨在直接解决以上问题：

1.  **显存减半:** INT8 格式的 KV Cache 理论上可将此部分的显存占用减少50%。
2.  **带宽减半:** 从 HBM 读取的数据量减半，可以缓解带宽压力，从而可能提升长序列场景下的推理速度。

---

## 2. vLLM KV Cache 量化核心实现

vLLM 采用的是一种**在线/动态（On-the-Fly）**的量化方案。它在 Attention 计算的流程中动态地对 KV Cache 进行量化和反量化，而不是改变其存储的数据类型。

**核心源码路径:**
*   `vllm/model_executor/layers/quantization/kv_cache.py`
*   `vllm/model_executor/models/llama.py` (以 Llama 为例)
*   `vllm/attention/ops/paged_attention.py`

### 2.1. 核心组件与量化策略

vLLM 的 KV Cache 量化是通过**缩放因子（Scaling Factor）**来实现的。

*   **`KVCacheScalingFactorBase` (in `kv_cache.py`):**
    *   定义了缩放因子计算的抽象基类。
    *   **量化公式:** `quantized_value = float_value / scale`
    *   **反量化公式:** `float_value = quantized_value * scale`

vLLM 提供了两种具体的缩放策略：

1.  **`PerTensorKVCacheScalingFactor`:**
    *   **策略:** 对整个 KV Cache 张量（跨所有 Token 和所有 Head）计算一个**单一的、共享的**缩放因子。
    *   **计算:** `scale = amax(abs(kv_cache)) / 127.0`。
    *   **优点:** 计算简单，开销极小。
    *   **缺点:** 对离群点（outliers）敏感。如果 Cache 中存在个别数值很大的元素，会导致 `scale` 过大，使得大部分数值在量化后都挤在0附近，造成严重的精度损失。

2.  **`PerTokenKVCacheScalingFactor`:**
    *   **策略:** 为**每个 Token** 计算一个独立的缩放因子。
    *   **计算:** 沿 Head 和 Head_dim 维度计算 `amax`，得到一个与 Token 数量相同的 `scale` 向量。
    *   **优点:** 能够更精细地适应不同 Token 的数值范围，鲁棒性更强，精度损失通常更小。这是 vLLM **默认和推荐**的策略。
    *   **缺点:** 计算和存储缩放因子的开销略高于 Per-Tensor 策略。

### 2.2. 端到端工作流程

KV Cache 的量化与反量化过程被无缝地集成在 Attention 计算的 CUDA Kernel 中。

**流程图:**
```
+----------------------+
|  LlamaAttention.fwd  |
+----------+-----------+
           |
           v
+----------------------+
|  Compute Q, K, V     |  (FP16/BF16)
|  (from hidden_states)|
+----------+-----------+
           |
           v
+----------------------+
| kv_scale_func(K, V)  |  (Compute scales, FP16/BF16)
+----------+-----------+
           |
           v
+----------------------+
| reshape_and_cache    |  (CUDA Kernel)
| - Quantize K, V      |  (FP16 -> INT8)
| - Write to Cache     |
+----------+-----------+
           |
           v
+----------------------+
| paged_attention_v1   |  (CUDA Kernel)
| - Read Q (FP16)      |
| - Read K_cache (INT8)|
| - Read K_scales(FP16)|
| - Dequantize K, V    |  (INT8 -> FP16, on-the-fly)
| - Compute Attention  |
+----------------------+
           |
           v
+----------------------+
|   Output (FP16)      |
+----------------------+
```

**详细步骤分解:**

1.  **启用配置:**
    *   在启动 `LLMEngine` 时，通过 `EngineArgs` 设置 `kv_cache_dtype="int8"`。
    *   vLLM 会在模型 `Attention` 层的 `__init__` 方法中，根据此配置实例化 `PerTokenKVCacheScalingFactor`。
    *   **源码 (`LlamaAttention.__init__`):**
        ```python
        if kv_cache_dtype == "int8" and not is_cpu:
            self.kv_scale_func = PerTokenKVCacheScalingFactor(...)
        ```

2.  **计算与量化 (写入Cache):**
    *   在 `LlamaAttention.forward` 中，模型首先计算出 FP16/BF16 格式的 `q`, `k`, `v` 向量。
    *   `k` 和 `v` 向量，连同它们新计算出的 `scales`，被传递给 `reshape_and_cache` CUDA Kernel。
    *   这个 Kernel **在内部完成量化** (`k_quant = k / k_scale`)，并将 INT8 结果写入由 PagedAttention 管理的物理块中。
    *   **源码 (`LlamaAttention.forward`):**
        ```python
        # k, v are in FP16/BF16
        k_scale, v_scale = self.kv_scale_func(k, v)
        self.reshape_and_cache(k, v, k_cache, v_cache, self.slot_mapping,
                               k_scale, v_scale)
        ```

3.  **反量化与计算 (读取Cache):**
    *   核心的 `paged_attention_v1` Kernel 被调用。
    *   它接收 FP16 的 `q`，以及 INT8 的 `k_cache`, `v_cache` 和 FP16 的 `scales`。
    *   在 Kernel 内部，计算注意力分数之前，会**即时（on-the-fly）地进行反量化**：`k_float = k_quantized * k_scale`。
    *   所有后续的 Attention 计算（`Q*K^T` 和 `Score*V`）都在 FP16/BF16 精度下进行。

---

## 3. 性能与精度权衡分析

*   **优点:**
    1.  **实现优雅，无缝集成:** 该方案不改变核心 Attention 的计算逻辑，仅在数据读写层进行转换，对现有架构的侵入性极小。
    2.  **兼容性强:** 由于计算仍在 FP16 下进行，因此与 FlashAttention 等优化可以很好地兼容。
    3.  **显著节省显存:** 对于长上下文场景，能够将服务的最大上下文长度或批次大小提升近一倍。

*   **缺点/开销:**
    1.  **计算开销:** 增加了计算缩放因子、量化和反量化的额外计算。
    2.  **精度损失:** 尽管 Per-Token 策略能缓解，但量化本身必然会引入精度误差。对于对精度极其敏感的任务，需要进行仔细评估。

*   **性能结论:**
    *   对于**短序列**或**计算密集型（compute-bound）**场景，KV Cache 量化带来的额外计算开销可能会抵消其带宽优势，甚至导致性能下降。
    *   对于**长序列**或**内存带宽密集型（memory-bound）**场景，节省的 HBM 读写时间通常能覆盖额外的计算开销，从而带来**端到端的性能提升**。

---

## 4. 预研结论与后续步骤

*   **结论:** vLLM 的 KV Cache 量化方案是一个设计优良且实用的功能，特别适用于需要支持长上下文的服务场景。它通过在线量化与反量化的方式，在精度和性能之间取得了很好的平衡。
*   **后续工作:**
    1.  **精度评测:** 针对具体业务场景，对比开启 KV Cache 量化前后的模型精度（如使用 LM-Harness 工具）。
    2.  **性能压测:** 使用 `benchmarks/benchmark_throughput.py` 脚本，测试不同上下文长度和并发数下，开启 KV Cache 量化对吞吐量和延迟的实际影响，找到性能提升的“甜点区”。
    3.  **探索 FP8 KV Cache:** vLLM 源码中已包含 FP8 相关工具（`fp8_utils.py`），可以预研在支持 FP8 的硬件（如 H100）上，使用 FP8 KV Cache 的可行性和性能表现。
