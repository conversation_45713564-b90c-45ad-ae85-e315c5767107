
# 学习文档: vLLM 框架核心

本文档旨在深入解析vLLM的核心架构，帮助您理解其高性能背后的关键技术。

---

## 1. vLLM 核心架构概览

vLLM 的设计目标是最大化LLM服务的吞吐量。它通过以下几个关键技术实现这一目标：

*   **PagedAttention:** 一种新颖的注意力算法，将显存管理的思想从操作系统借鉴而来，有效解决了KV Cache的内存碎片问题。
*   **Continuous Batching:** 与传统的静态批处理不同，它允许在批处理处理过程中动态插入新的请求，从而提高GPU利用率。
*   **优化的CUDA Kernels:** vLLM为关键操作（如PagedAttention）编写了高度优化的CUDA内核，以实现极致性能。

### 架构图

```
+---------------------+      +-------------------+      +---------------------+
|      API Server     |----->|     LLMEngine     |<---->|      Scheduler      |
| (e.g., FastAPI)     |      | (Central Control) |      | (Continuous Batching)|
+---------------------+      +-------------------+      +----------+----------+
                                      |                             |
                                      |                             |
                                      v                             v
+-----------------------------------------------------------------------------+
|                                Worker Group                                 |
|                                                                             |
| +-----------------+   +-----------------+   +-----------------+   +---------+
| |    Worker 0     |   |    Worker 1     |   |    Worker 2     |...| Worker N|
| | (GPU 0)         |   | (GPU 1)         |   | (GPU 2)         |   | (GPU N) |
| | +-------------+ |   | +-------------+ |   | +-------------+ |   | +-------+
| | | Model Exec  | |   | | Model Exec  | |   | | Model Exec  | |   | | Model |
| | +-------------+ |   | +-------------+ |   | +-------------+ |   | +-------+
| | | CacheEngine | |   | | CacheEngine | |   | | CacheEngine | |   | | Cache |
| | +-------------+ |   | +-------------+ |   | +-------------+ |   | +-------+
| +-----------------+   +-----------------+   +-----------------+   +---------+
|                                                                             |
+-----------------------------------------------------------------------------+
```

---

## 2. 请求的生命周期

理解一个请求在vLLM中的完整流程是掌握其工作原理的关键。

**核心源码路径:** `vllm/engine/llm_engine.py`, `vllm/core/scheduler.py`, `vllm/worker/worker.py`

1.  **接收请求:**
    *   请求通过API服务器（如使用`vllm.entrypoints.api_server`启动）或直接调用`LLMEngine.add_request()`进入系统。
    *   每个请求被封装成一个`SequenceGroup`对象，其中包含输入的`prompt_token_ids`、采样参数`SamplingParams`等信息。
    *   `SequenceGroup`被放入一个待处理队列`self.pending_requests`中。
    *   **源码:** `vllm/engine/llm_engine.py: LLMEngine.add_request()`

2.  **调度 (Scheduling):**
    *   `LLMEngine`的主循环会定期调用`self.scheduler.schedule()`。
    *   **调度器 (`Scheduler`)** 是vLLM的大脑。它的核心职责是决定哪些请求（`SequenceGroup`）可以进入下一个批处理。
    *   **决策依据:**
        *   **内存检查:** 调度器会检查`CacheEngine`是否有足够的空闲块（block）来为新请求分配KV Cache。
        *   **抢占 (Preemption):** 如果内存不足，调度器可能会根据优先级（如`Priority.HIGH`）抢占（暂停）正在运行的低优先级请求，释放其占用的块，以便为新请求提供服务。被抢占的请求会被放回等待队列。
        *   **Continuous Batching:** 调度器会将等待队列中的请求和正在运行但尚未完成的请求组合成一个新的批次（`batch`）。
    *   **输出:** 调度器的输出是一个`ScheduledSequenceGroup`列表和`SchedulerOutputs`对象，其中包含了下一个批次的详细信息，如需要分配的块、需要交换（swap in/out）的块、需要复制的块等。
    *   **源码:** `vllm/core/scheduler.py: Scheduler.schedule()`

3.  **执行 (Execution):**
    *   `LLMEngine`将调度结果分发给`Worker`组。在分布式设置中，这通过Ray或其他后端完成。
    *   每个**Worker**（通常对应一个GPU）接收到指令后，执行模型的前向传播。
    *   **核心步骤 (`Worker.execute_model()`):**
        1.  **内存操作:** Worker首先根据调度结果，通过`self.cache_engine`执行KV Cache操作（分配、复制、交换）。
        2.  **准备输入:** 准备`input_tokens`, `input_positions`, 和 `block_tables`。`block_tables`是PagedAttention的关键，它告诉GPU每个token的KV数据应该写入哪个物理内存块。
        3.  **模型前向:** 调用`self.model_runner.execute_model()`，这会执行实际的模型计算。在这一步，PagedAttention CUDA Kernel被调用。
        4.  **返回结果:** Worker返回计算出的logits。
    *   **源码:** `vllm/worker/worker.py: Worker.execute_model()`

4.  **采样与后处理:**
    *   `LLMEngine`收集所有Worker返回的logits。
    *   对于每个序列，执行采样操作（Greedy, Beam Search等）以生成新的token。
    *   更新序列的状态，检查是否满足停止条件（如生成了EOS token或达到`max_tokens`）。
    *   完成的序列将从运行队列中移除，其结果将被返回给用户。
    *   未完成的序列将留在运行队列中，等待下一轮调度。
    *   **源码:** `vllm/engine/llm_engine.py: LLMEngine._process_model_outputs()`

---

## 3. 核心技术深度解析

### 3.1. PagedAttention

*   **问题:** 传统的KV Cache内存管理方式（如HuggingFace Transformers）为每个序列预先分配一个连续的、最大长度的内存块。这导致了严重的**内部碎片**（如果序列很短）和**外部碎片**（当长序列结束后，短序列无法利用释放的大块内存）。
*   **vLLM的解决方案:**
    *   **分页 (Paging):** vLLM将每个序列的KV Cache分割成固定大小的**块 (block)**。
    *   **逻辑块 vs 物理块:**
        *   **逻辑块:** 从序列的角度看，它的KV Cache是连续的逻辑块。
        *   **物理块:** 在GPU显存中，这些块可以存储在任意不连续的位置。
    *   **块表 (Block Table):** 每个序列都维护一个“块表”，这是一个映射，告诉GPU第`i`个逻辑块对应哪个物理块。
    *   **优点:**
        *   **近乎零的内存浪费:** 内存以块为单位进行管理，大大减少了碎片。
        *   **灵活的内存共享:** 像Beam Search或并行采样中，多个序列可以共享相同的prompt部分的KV Cache块，只需复制块表并为新生成的部分分配新块即可，实现了写时复制（Copy-on-Write）。
*   **源码:**
    *   `vllm/core/cache_engine.py`: 管理物理块的分配和释放。
    *   `vllm/worker/worker.py`: `prepare_input`方法中构建块表。
    *   `vllm/attention/ops/paged_attention.py`: PagedAttention的CUDA Kernel接口。

### 3.2. Continuous Batching

*   **问题:** 静态批处理必须等待批次中的所有序列都完成后才能开始下一个批次，导致GPU在等待最长序列完成时处于空闲状态。
*   **vLLM的解决方案:**
    *   **迭代式调度:** vLLM的调度器在每个时间步都会重新评估。
    *   **动态添加:** 一旦批次中的某个序列完成，调度器会立即尝试从等待队列中添加一个新的序列来填补空白，只要内存允许。
    *   **结果:** 这种“持续不断”的批处理方式使得GPU利用率始终保持在高位，从而显著提高吞吐量。
*   **源码:** `vllm/core/scheduler.py` 的 `schedule` 方法是这一机制的核心实现。

---

## 4. 与 LLM Compressor 的关系

*   **vLLM是“运行时” (Runtime):** 它不关心模型是如何被量化的，只关心如何高效地**执行**一个（可能是量化的）模型。
*   **LLM Compressor是“编译器” (Compiler):** 它负责将一个FP16模型通过各种算法（量化、剪枝等）**转换**成一个更小、更快的模型。

**典型的合作流程:**

1.  **在LLM Compressor中:**
    *   定义一个`recipe.yaml`，指定压缩策略（如SmoothQuant + INT8量化）。
    *   运行`llmcompressor.oneshot`，输入FP16模型和校准数据。
    *   导出一个量化模型（包含`pytorch_model.bin`和`quantization_config.json`等文件）。
2.  **在vLLM中:**
    *   初始化`LLMEngine`时，将`model`参数指向导出的模型目录。
    *   vLLM的`get_model`函数会自动检测到`quantization_config.json`，并加载相应的`QuantizationMethod`（如`AWQLinearMethod`）。
    *   `QuantizationMethod`会用高性能的量化层（如`MarlinLinear`）替换模型中的`nn.Linear`层。
    *   vLLM开始提供服务，所有推理都在量化模型上以高性能执行。
