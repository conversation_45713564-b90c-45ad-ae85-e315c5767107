# 量化流程可视化图表

## 🔄 完整量化流程图

```mermaid
graph TD
    A[开始量化] --> B[SmoothQuant阶段]
    B --> C[映射推断]
    C --> D[激活统计收集]
    D --> E[平滑因子计算]
    E --> F[权重变换应用]
    F --> G[GPTQ阶段]
    G --> H[Hessian累积]
    H --> I[逐层量化]
    I --> J[参数更新]
    J --> K[量化完成]
    
    C --> C1[注意力层映射<br/>q/k/v_proj ← input_layernorm]
    C --> C2[MLP层映射<br/>gate/up_proj ← post_attention_layernorm]
    
    E --> E1[α=0.8平滑强度]
    E --> E2[s = max|X|^α / max|W|^(1-α)]
    
    F --> F1[Linear层: W = W * s]
    F --> F2[LayerNorm层: w = w / s]
    
    I --> I1[q_proj: 损失0.00386541]
    I --> I2[k_proj: 损失0.00397363]
    I --> I3[v_proj: 损失0.00370757]
    I --> I4[o_proj: 损失0.00000463]
    I --> I5[gate_proj: 损失0.00065096]
    I --> I6[up_proj: 损失0.00094338]
    I --> I7[down_proj: 损失0.00000000]
```

## 📊 层级映射关系图

```mermaid
graph LR
    subgraph "注意力层映射"
        A1[input_layernorm<br/>平滑层] --> B1[q_proj<br/>balance层]
        A1 --> B2[k_proj<br/>balance层]
        A1 --> B3[v_proj<br/>balance层]
        B1 --> C1[o_proj<br/>未受影响]
        B2 --> C1
        B3 --> C1
    end
    
    subgraph "MLP层映射"
        A2[post_attention_layernorm<br/>平滑层] --> B4[gate_proj<br/>balance层]
        A2 --> B5[up_proj<br/>balance层]
        B4 --> C2[down_proj<br/>未受影响]
        B5 --> C2
    end
    
    style C1 fill:#e1f5fe
    style C2 fill:#e1f5fe
```

## 🔧 SmoothQuant变换过程

```mermaid
graph TD
    A[激活统计收集] --> B[计算激活缩放因子<br/>max_vals - min_vals]
    B --> C[计算权重缩放因子<br/>2.0 * max(abs(W))]
    C --> D[计算平滑因子<br/>s = X_scale^α / W_scale^(1-α)]
    D --> E[应用到Linear层<br/>W = W * s]
    D --> F[应用到LayerNorm层<br/>w = w / s, b = b / s]
    
    E --> E1[q_proj权重调整<br/>std: 0.019974 → 0.048401]
    E --> E2[k_proj权重调整<br/>std: 0.019928 → 0.048035]
    E --> E3[v_proj权重调整<br/>std: 0.020065 → 0.048035]
    E --> E4[gate_proj权重调整<br/>std: 0.019669 → 0.044800]
    E --> E5[up_proj权重调整<br/>std: 0.019730 → 0.047760]
    
    F --> F1[input_layernorm调整<br/>mean: 1.0 → 0.623535]
    F --> F2[post_attention_layernorm调整<br/>mean: 1.0 → 0.492188]
```

## 🎯 GPTQ量化过程

```mermaid
graph TD
    A[校准阶段] --> B[Hessian矩阵累积<br/>H = Σ(x_i * x_i^T)]
    B --> C[量化阶段]
    C --> D[计算Hessian逆矩阵<br/>H_inv = (H + λI)^(-1)]
    D --> E[块级量化处理<br/>block_size=128]
    E --> F[计算量化参数<br/>scale, zero_point]
    F --> G[权重量化<br/>q = round(w/scale)]
    G --> H[误差传播<br/>使用Hessian信息]
    H --> I[参数更新]
    
    I --> I1[weight: FP16量化值]
    I --> I2[weight_scale: FP16缩放因子]
    I --> I3[weight_zero_point: INT8零点]
```

## 📈 量化精度对比图

```mermaid
graph LR
    subgraph "量化损失 (从高到低)"
        A1[k_proj<br/>0.00397363] --> A2[q_proj<br/>0.00386541]
        A2 --> A3[v_proj<br/>0.00370757]
        A3 --> A4[up_proj<br/>0.00094338]
        A4 --> A5[gate_proj<br/>0.00065096]
        A5 --> A6[o_proj<br/>0.00000463]
        A6 --> A7[down_proj<br/>0.00000000]
    end
    
    subgraph "SmoothQuant影响"
        B1[受影响层<br/>q/k/v/gate/up_proj] 
        B2[未受影响层<br/>o_proj/down_proj]
    end
    
    A1 -.-> B1
    A2 -.-> B1
    A3 -.-> B1
    A4 -.-> B1
    A5 -.-> B1
    A6 -.-> B2
    A7 -.-> B2
    
    style A6 fill:#c8e6c9
    style A7 fill:#a5d6a7
    style B2 fill:#e8f5e8
```

## 🔍 o_proj和down_proj特殊性分析

```mermaid
graph TD
    A[为什么o_proj和down_proj量化效果最好？] --> B[原因分析]
    
    B --> C1[不受SmoothQuant影响<br/>不在balance_layers中]
    B --> C2[权重分布特征好<br/>相对均匀，适合量化]
    B --> C3[网络位置优势<br/>作为输出层，误差敏感性低]
    B --> C4[GPTQ算法优势<br/>Hessian信息优化量化]
    
    C1 --> D1[保持原始权重分布<br/>未被平滑变换调整]
    C2 --> D2[o_proj: std=0.020020<br/>down_proj: std=0.020050]
    C3 --> D3[输出层特性<br/>对精度要求相对宽松]
    C4 --> D4[二阶信息指导<br/>最优量化参数选择]
    
    D1 --> E[量化损失极低]
    D2 --> E
    D3 --> E
    D4 --> E
    
    E --> F1[o_proj: 0.00000463]
    E --> F2[down_proj: 0.00000000]
    
    style E fill:#ffecb3
    style F1 fill:#c8e6c9
    style F2 fill:#a5d6a7
```

## 📊 参数变化统计图

```mermaid
graph LR
    subgraph "原始模型"
        A1[总参数: 1,091,152<br/>全部FP16]
    end
    
    subgraph "量化模型"
        B1[总参数: 1,097,488<br/>FP16: 1,094,320<br/>INT8: 3,168]
    end
    
    subgraph "新增量化参数"
        C1[weight_scale: 7层×通道数<br/>FP16格式]
        C2[weight_zero_point: 7层×通道数<br/>INT8格式]
    end
    
    A1 --> B1
    B1 --> C1
    B1 --> C2
    
    C1 --> D1[q_proj: 1024个scale]
    C1 --> D2[k_proj: 1024个scale]
    C1 --> D3[v_proj: 1024个scale]
    C1 --> D4[o_proj: 16个scale]
    C1 --> D5[gate_proj: 32个scale]
    C1 --> D6[up_proj: 32个scale]
    C1 --> D7[down_proj: 16个scale]
```

## 🎯 代码执行时序图

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant OS as oneshot()
    participant SQ as SmoothQuant
    participant GPTQ as GPTQModifier
    participant QW as quantize_weight()
    
    User->>OS: 调用oneshot()
    OS->>SQ: 初始化SmoothQuant
    SQ->>SQ: _infer_mappings_from_model()
    SQ->>SQ: 收集激活统计 (32样本)
    SQ->>SQ: _calculate_smoothing_scales()
    SQ->>SQ: _apply_smoothing()
    
    OS->>GPTQ: 初始化GPTQ
    GPTQ->>GPTQ: 注册校准钩子
    GPTQ->>GPTQ: calibrate_module() (累积Hessian)
    GPTQ->>GPTQ: compress_modules()
    
    loop 7个层
        GPTQ->>QW: quantize_weight()
        QW->>QW: 计算Hessian逆矩阵
        QW->>QW: 块级量化 (block_size=128)
        QW->>QW: 计算scale和zero_point
        QW-->>GPTQ: 返回量化结果
        GPTQ->>GPTQ: 更新模块参数
    end
    
    GPTQ-->>OS: 量化完成
    OS-->>User: 返回量化模型
```

这些可视化图表清晰地展示了量化过程的各个阶段、层级关系和关键技术细节。
