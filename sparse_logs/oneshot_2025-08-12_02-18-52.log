2025-08-12 02:18:52.682 | DEBUG    | llmcompressor.core.lifecycle:reset:61 - Resetting compression lifecycle
2025-08-12 02:18:52.682 | INFO     | llmcompressor.core.lifecycle:reset:73 - Compression lifecycle reset
2025-08-12 02:18:52.682 | DEBUG    | llmcompressor.core.state:update:182 - Updating state with provided parameters: {'model': LlamaForCausalLM(
  (model): LlamaModel(
    (embed_tokens): Embedding(32001, 16)
    (layers): ModuleList(
      (0): LlamaDecoderLayer(
        (self_attn): LlamaAttention(
          (q_proj): Linear(in_features=16, out_features=1024, bias=False)
          (k_proj): Linear(in_features=16, out_features=1024, bias=False)
          (v_proj): Linear(in_features=16, out_features=1024, bias=False)
          (o_proj): Linear(in_features=1024, out_features=16, bias=False)
        )
        (mlp): LlamaMLP(
          (gate_proj): Linear(in_features=16, out_features=32, bias=False)
          (up_proj): Linear(in_features=16, out_features=32, bias=False)
          (down_proj): Linear(in_features=32, out_features=16, bias=False)
          (act_fn): SiLU()
        )
        (input_layernorm): LlamaRMSNorm((16,), eps=1e-05)
        (post_attention_layernorm): LlamaRMSNorm((16,), eps=1e-05)
      )
    )
    (norm): LlamaRMSNorm((16,), eps=1e-05)
    (rotary_emb): LlamaRotaryEmbedding()
  )
  (lm_head): Linear(in_features=16, out_features=32001, bias=False)
), 'teacher_model': None, 'optimizer': None, 'attach_optim_callbacks': True, 'train_data': None, 'val_data': None, 'test_data': None, 'calib_data': <torch.utils.data.dataloader.DataLoader object at 0x7da2a52f0ca0>, 'copy_data': True, 'start': -1, 'steps_per_epoch': None, 'batches_per_step': None, 'loggers': None, 'model_log_cadence': None, 'kwargs': {}}
2025-08-12 02:18:52.683 | DEBUG    | llmcompressor.core.lifecycle:initialize:94 - Initializing compression lifecycle
2025-08-12 02:18:52.684 | INFO     | llmcompressor.recipe.recipe:from_modifiers:59 - Creating recipe from modifiers
2025-08-12 02:18:52.709 | INFO     | llmcompressor.modifiers.smoothquant.base:_infer_mappings_from_model:183 - No SmoothQuantModifier.mappings provided, inferring from model...
2025-08-12 02:18:52.712 | DEBUG    | llmcompressor.core.lifecycle:initialize:103 - Initialized modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=False, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None), GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=False, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=0 group='default' applied=False
2025-08-12 02:18:52.712 | INFO     | llmcompressor.core.lifecycle:initialize:108 - Compression lifecycle initialized for 1 modifiers
2025-08-12 02:18:52.712 | INFO     | llmcompressor.pipelines.independent.pipeline:IndependentPipeline:47 - Inferred `SequentialPipeline` for `SmoothQuantModifier`
2025-08-12 02:18:53.189 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.189 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if labels is not None:
    loss = self.loss_function(logits=logits, labels=labels, vocab_size=self.config.vocab_size, **kwargs)
2025-08-12 02:18:53.189 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.193 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if (input_ids is None) ^ (inputs_embeds is not None):
    raise ValueError('You must specify exactly one of input_ids or inputs_embeds')
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if not isinstance(past_key_values, (type(None), Cache)):
    raise ValueError('The `past_key_values` should be either a `Cache` object or `None`.')
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if inputs_embeds is None:
    inputs_embeds = self.embed_tokens(input_ids)
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if use_cache and past_key_values is None:
    past_key_values = DynamicCache()
2025-08-12 02:18:53.194 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if cache_position is None:
    past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
    cache_position = torch.arange(past_seen_tokens, past_seen_tokens + inputs_embeds.shape[1], device=inputs_embeds.device)
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if position_ids is None:
    position_ids = cache_position.unsqueeze(0)
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:189 - ---- Autowrapper ----
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:190 - self._update_causal_mask(attention_mask, inputs_embeds, cache_position, past_key_values, output_attentions)
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:191 - ---------------------
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-12 02:18:53.195 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.196 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.196 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_attentions:
    all_self_attns += (layer_outputs[1],)
2025-08-12 02:18:53.196 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.196 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.196 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-12 02:18:53.196 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.216 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_START
2025-08-12 02:18:53.217 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - index=0 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False smoothing_strength=0.8 mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')] ignore=[] num_calibration_steps=None calibration_function=None added <torch.utils.hooks.RemovableHandle object at 0x7da290d6b0d0>
2025-08-12 02:18:53.217 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - index=0 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False smoothing_strength=0.8 mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')] ignore=[] num_calibration_steps=None calibration_function=None added <torch.utils.hooks.RemovableHandle object at 0x7da290d6af80>
2025-08-12 02:18:53.217 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - index=0 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False smoothing_strength=0.8 mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')] ignore=[] num_calibration_steps=None calibration_function=None added <torch.utils.hooks.RemovableHandle object at 0x7da290d6ae30>
2025-08-12 02:18:53.217 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - index=0 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False smoothing_strength=0.8 mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')] ignore=[] num_calibration_steps=None calibration_function=None added <torch.utils.hooks.RemovableHandle object at 0x7da290d6ac50>
2025-08-12 02:18:53.217 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-12 02:18:53.636 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-12 02:18:53.660 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-12 02:18:53.679 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-12 02:18:53.680 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-12 02:18:53.682 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_END
2025-08-12 02:18:53.682 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=True, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-12 02:18:53.683 | INFO     | llmcompressor.pipelines.independent.pipeline:IndependentPipeline:47 - Inferred `SequentialPipeline` for `GPTQModifier`
2025-08-12 02:18:53.689 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.690 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if labels is not None:
    loss = self.loss_function(logits=logits, labels=labels, vocab_size=self.config.vocab_size, **kwargs)
2025-08-12 02:18:53.690 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if (input_ids is None) ^ (inputs_embeds is not None):
    raise ValueError('You must specify exactly one of input_ids or inputs_embeds')
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if not isinstance(past_key_values, (type(None), Cache)):
    raise ValueError('The `past_key_values` should be either a `Cache` object or `None`.')
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if inputs_embeds is None:
    inputs_embeds = self.embed_tokens(input_ids)
2025-08-12 02:18:53.693 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if use_cache and past_key_values is None:
    past_key_values = DynamicCache()
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if cache_position is None:
    past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
    cache_position = torch.arange(past_seen_tokens, past_seen_tokens + inputs_embeds.shape[1], device=inputs_embeds.device)
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if position_ids is None:
    position_ids = cache_position.unsqueeze(0)
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:189 - ---- Autowrapper ----
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:190 - self._update_causal_mask(attention_mask, inputs_embeds, cache_position, past_key_values, output_attentions)
2025-08-12 02:18:53.694 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:191 - ---------------------
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_attentions:
    all_self_attns += (layer_outputs[1],)
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-12 02:18:53.695 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_START
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7da290d69cf0>
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7da290d6a500>
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7da290d69ed0>
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7da290d6a740>
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7da290d69ea0>
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7da290d6a650>
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7da290d6bf40>
2025-08-12 02:18:53.709 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-12 02:18:53.740 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-12 02:18:53.740 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.q_proj using 8 samples
2025-08-12 02:18:53.819 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.08s
2025-08-12 02:18:53.819 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.80% | total memory: 42 GB
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 23.52% | total memory: 42 GB
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 1.28% | total memory: 42 GB
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 91.80% | total memory: 42 GB
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:53.820 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-12 02:18:53.820 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.k_proj using 8 samples
2025-08-12 02:18:53.827 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-12 02:18:53.827 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-12 02:18:53.827 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.80% | total memory: 42 GB
2025-08-12 02:18:53.827 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 23.52% | total memory: 42 GB
2025-08-12 02:18:53.828 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 1.28% | total memory: 42 GB
2025-08-12 02:18:53.828 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:53.828 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 91.80% | total memory: 42 GB
2025-08-12 02:18:53.828 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:53.828 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:53.828 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:53.828 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-12 02:18:53.828 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.v_proj using 8 samples
2025-08-12 02:18:53.834 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-12 02:18:53.834 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-12 02:18:53.834 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.80% | total memory: 42 GB
2025-08-12 02:18:53.834 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 23.52% | total memory: 42 GB
2025-08-12 02:18:53.834 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 1.28% | total memory: 42 GB
2025-08-12 02:18:53.834 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:53.834 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 91.80% | total memory: 42 GB
2025-08-12 02:18:53.834 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:53.835 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:53.835 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:53.835 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-12 02:18:53.835 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.o_proj using 8 samples
2025-08-12 02:18:54.142 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.31s
2025-08-12 02:18:54.143 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.85% | total memory: 42 GB
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 23.52% | total memory: 42 GB
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 1.28% | total memory: 42 GB
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 91.80% | total memory: 42 GB
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:54.144 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.032816 MB
2025-08-12 02:18:54.144 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.gate_proj using 8 samples
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.85% | total memory: 42 GB
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 23.52% | total memory: 42 GB
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 1.28% | total memory: 42 GB
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 91.80% | total memory: 42 GB
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:54.151 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.00112 MB
2025-08-12 02:18:54.151 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.up_proj using 8 samples
2025-08-12 02:18:54.157 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.85% | total memory: 42 GB
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 23.52% | total memory: 42 GB
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 1.28% | total memory: 42 GB
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 91.80% | total memory: 42 GB
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:54.158 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.00112 MB
2025-08-12 02:18:54.158 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.down_proj using 8 samples
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.85% | total memory: 42 GB
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 23.52% | total memory: 42 GB
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 1.28% | total memory: 42 GB
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 91.80% | total memory: 42 GB
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 6.96% | total memory: 42 GB
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 1.27% | total memory: 42 GB
2025-08-12 02:18:54.169 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.001072 MB
2025-08-12 02:18:54.170 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-12 02:18:54.187 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-12 02:18:54.187 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-12 02:18:54.190 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_END
2025-08-12 02:18:54.190 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=True, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-12 02:18:54.190 | DEBUG    | llmcompressor.core.lifecycle:finalize:131 - Finalizing compression lifecycle
2025-08-12 02:18:54.190 | DEBUG    | llmcompressor.core.lifecycle:finalize:135 - Finalized modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=True, started_=True, ended_=True, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None), GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=True, started_=True, ended_=True, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=0 group='default' applied=True
2025-08-12 02:18:54.190 | INFO     | llmcompressor.core.lifecycle:finalize:141 - Compression lifecycle finalized for 1 modifiers
2025-08-12 02:18:54.192 | WARNING  | llmcompressor.entrypoints.utils:post_process:107 - Optimized model is not saved. To save, please provide`output_dir` as input arg.Ex. `oneshot(..., output_dir=...)`
