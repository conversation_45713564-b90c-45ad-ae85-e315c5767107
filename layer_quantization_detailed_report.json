{"model_states": {"original": {"stage": "original", "layers": {"model.layers.0.self_attn.q_proj": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.self_attn.k_proj": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.self_attn.v_proj": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.self_attn.o_proj": {"weight_shape": [16, 1024], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.mlp.gate_proj": {"weight_shape": [32, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.mlp.up_proj": {"weight_shape": [32, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.mlp.down_proj": {"weight_shape": [16, 32], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.input_layernorm": {"weight_shape": [16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.post_attention_layernorm": {"weight_shape": [16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}}, "total_params": 1091152, "param_count_by_dtype": {"torch.float16": 1091152}}, "quantized": {"stage": "quantized", "layers": {"model.layers.0.self_attn.q_proj": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": true, "quantization_params": {"weight_scale": {"shape": [1024, 1], "dtype": "torch.float16", "device": "cuda:0"}, "weight_zero_point": {"shape": [1024, 1], "dtype": "torch.int8", "device": "cuda:0"}}}, "model.layers.0.self_attn.k_proj": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": true, "quantization_params": {"weight_scale": {"shape": [1024, 1], "dtype": "torch.float16", "device": "cuda:0"}, "weight_zero_point": {"shape": [1024, 1], "dtype": "torch.int8", "device": "cuda:0"}}}, "model.layers.0.self_attn.v_proj": {"weight_shape": [1024, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": true, "quantization_params": {"weight_scale": {"shape": [1024, 1], "dtype": "torch.float16", "device": "cuda:0"}, "weight_zero_point": {"shape": [1024, 1], "dtype": "torch.int8", "device": "cuda:0"}}}, "model.layers.0.self_attn.o_proj": {"weight_shape": [16, 1024], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": true, "quantization_params": {"weight_scale": {"shape": [16, 1], "dtype": "torch.float16", "device": "cuda:0"}, "weight_zero_point": {"shape": [16, 1], "dtype": "torch.int8", "device": "cuda:0"}}}, "model.layers.0.mlp.gate_proj": {"weight_shape": [32, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": true, "quantization_params": {"weight_scale": {"shape": [32, 1], "dtype": "torch.float16", "device": "cuda:0"}, "weight_zero_point": {"shape": [32, 1], "dtype": "torch.int8", "device": "cuda:0"}}}, "model.layers.0.mlp.up_proj": {"weight_shape": [32, 16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": true, "quantization_params": {"weight_scale": {"shape": [32, 1], "dtype": "torch.float16", "device": "cuda:0"}, "weight_zero_point": {"shape": [32, 1], "dtype": "torch.int8", "device": "cuda:0"}}}, "model.layers.0.mlp.down_proj": {"weight_shape": [16, 32], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": true, "quantization_params": {"weight_scale": {"shape": [16, 1], "dtype": "torch.float16", "device": "cuda:0"}, "weight_zero_point": {"shape": [16, 1], "dtype": "torch.int8", "device": "cuda:0"}}}, "model.layers.0.input_layernorm": {"weight_shape": [16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}, "model.layers.0.post_attention_layernorm": {"weight_shape": [16], "weight_dtype": "torch.float16", "bias_shape": null, "has_quantization_params": false, "quantization_params": {}}}, "total_params": 1097488, "param_count_by_dtype": {"torch.float16": 1094320, "torch.int8": 3168}}}, "smoothquant_operations": [{"layer_name": "model.layers.0.self_attn.q_proj", "operation_type": "weight_scaling", "details": {"original_stats": {"mean": 0.0001976490020751953, "std": 0.0199737548828125, "min": -0.073974609375, "max": 0.0767822265625}, "new_stats": {"mean": 0.00014472007751464844, "std": 0.04840087890625, "min": -0.274169921875, "max": 0.30322265625}, "scale_factor_applied": "scales.view(1, -1)"}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.self_attn.k_proj", "operation_type": "weight_scaling", "details": {"original_stats": {"mean": 2.8014183044433594e-05, "std": 0.019927978515625, "min": -0.0743408203125, "max": 0.074951171875}, "new_stats": {"mean": 0.00010776519775390625, "std": 0.04803466796875, "min": -0.277587890625, "max": 0.32568359375}, "scale_factor_applied": "scales.view(1, -1)"}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.self_attn.v_proj", "operation_type": "weight_scaling", "details": {"original_stats": {"mean": -0.00017845630645751953, "std": 0.0200653076171875, "min": -0.08697509765625, "max": 0.08367919921875}, "new_stats": {"mean": -0.0005512237548828125, "std": 0.04803466796875, "min": -0.26171875, "max": 0.2393798828125}, "scale_factor_applied": "scales.view(1, -1)"}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.input_layernorm", "operation_type": "layernorm_scaling", "details": {"original_stats": {"mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0}, "new_stats": {"mean": 0.62353515625, "std": 0.44970703125, "min": 0.230224609375, "max": 1.74609375}, "scale_factor_applied": "div_(scales)"}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.mlp.gate_proj", "operation_type": "weight_scaling", "details": {"original_stats": {"mean": -0.0004253387451171875, "std": 0.0196685791015625, "min": -0.06658935546875, "max": 0.05230712890625}, "new_stats": {"mean": -0.00028133392333984375, "std": 0.0447998046875, "min": -0.1297607421875, "max": 0.1402587890625}, "scale_factor_applied": "scales.view(1, -1)"}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.mlp.up_proj", "operation_type": "weight_scaling", "details": {"original_stats": {"mean": -0.0006008148193359375, "std": 0.0197296142578125, "min": -0.066650390625, "max": 0.0631103515625}, "new_stats": {"mean": -0.0020694732666015625, "std": 0.047760009765625, "min": -0.182861328125, "max": 0.135009765625}, "scale_factor_applied": "scales.view(1, -1)"}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.post_attention_layernorm", "operation_type": "layernorm_scaling", "details": {"original_stats": {"mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0}, "new_stats": {"mean": 0.4921875, "std": 0.1781005859375, "min": 0.253173828125, "max": 0.94970703125}, "scale_factor_applied": "div_(scales)"}, "timestamp": "<torch.cuda.Event uninitialized>"}], "gptq_operations": [{"layer_name": "model.layers.0.self_attn.q_proj", "operation_type": "quantization", "details": {"original_stats": {"shape": [1024, 16], "dtype": "torch.float16", "mean": 0.00014472007751464844, "std": 0.04840087890625, "min": -0.274169921875, "max": 0.30322265625}, "quantized_stats": {"shape": [1024, 16], "dtype": "torch.float16", "mean": 0.0001301963784499094, "std": 0.0483543761074543, "min": -0.275146484375, "max": 0.302001953125}, "scale_stats": {"shape": [1024, 1], "dtype": "torch.float16", "mean": 0.0008945465087890625, "std": 0.00029277801513671875, "min": 0.0002949237823486328, "max": 0.0023784637451171875}, "zero_point_stats": {"shape": [1024, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "loss": 0.003865408943966031, "block_size": 128, "dampening_frac": 0.01}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.self_attn.k_proj", "operation_type": "quantization", "details": {"original_stats": {"shape": [1024, 16], "dtype": "torch.float16", "mean": 0.00010776519775390625, "std": 0.04803466796875, "min": -0.277587890625, "max": 0.32568359375}, "quantized_stats": {"shape": [1024, 16], "dtype": "torch.float16", "mean": 9.459129069000483e-05, "std": 0.04798812419176102, "min": -0.276611328125, "max": 0.324462890625}, "scale_stats": {"shape": [1024, 1], "dtype": "torch.float16", "mean": 0.0008907318115234375, "std": 0.00030159950256347656, "min": 0.0003662109375, "max": 0.0025539398193359375}, "zero_point_stats": {"shape": [1024, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "loss": 0.003973633982241154, "block_size": 128, "dampening_frac": 0.01}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.self_attn.v_proj", "operation_type": "quantization", "details": {"original_stats": {"shape": [1024, 16], "dtype": "torch.float16", "mean": -0.0005512237548828125, "std": 0.04803466796875, "min": -0.26171875, "max": 0.2393798828125}, "quantized_stats": {"shape": [1024, 16], "dtype": "torch.float16", "mean": -0.0005648712394759059, "std": 0.0479901060461998, "min": -0.2607421875, "max": 0.2384033203125}, "scale_stats": {"shape": [1024, 1], "dtype": "torch.float16", "mean": 0.0008854866027832031, "std": 0.00028228759765625, "min": 0.00035119056701660156, "max": 0.00205230712890625}, "zero_point_stats": {"shape": [1024, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "loss": 0.0037075746804475784, "block_size": 128, "dampening_frac": 0.01}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.self_attn.o_proj", "operation_type": "quantization", "details": {"original_stats": {"shape": [16, 1024], "dtype": "torch.float16", "mean": -7.790327072143555e-05, "std": 0.02001953125, "min": -0.07513427734375, "max": 0.08050537109375}, "quantized_stats": {"shape": [16, 1024], "dtype": "torch.float16", "mean": -7.767394708935171e-05, "std": 0.0200189296156168, "min": -0.075439453125, "max": 0.0802001953125}, "scale_stats": {"shape": [16, 1], "dtype": "torch.float16", "mean": 0.0005459785461425781, "std": 4.655122756958008e-05, "min": 0.00047516822814941406, "max": 0.0006313323974609375}, "zero_point_stats": {"shape": [16, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "loss": 4.627048838301562e-06, "block_size": 128, "dampening_frac": 0.01}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.mlp.gate_proj", "operation_type": "quantization", "details": {"original_stats": {"shape": [32, 16], "dtype": "torch.float16", "mean": -0.00028133392333984375, "std": 0.0447998046875, "min": -0.1297607421875, "max": 0.1402587890625}, "quantized_stats": {"shape": [32, 16], "dtype": "torch.float16", "mean": -0.00027909036725759506, "std": 0.04477342963218689, "min": -0.1292724609375, "max": 0.1396484375}, "scale_stats": {"shape": [32, 1], "dtype": "torch.float16", "mean": 0.0007910728454589844, "std": 0.00016224384307861328, "min": 0.0004782676696777344, "max": 0.0011005401611328125}, "zero_point_stats": {"shape": [32, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "loss": 0.0006509586237370968, "block_size": 128, "dampening_frac": 0.01}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.mlp.up_proj", "operation_type": "quantization", "details": {"original_stats": {"shape": [32, 16], "dtype": "torch.float16", "mean": -0.0020694732666015625, "std": 0.047760009765625, "min": -0.182861328125, "max": 0.135009765625}, "quantized_stats": {"shape": [32, 16], "dtype": "torch.float16", "mean": -0.0020549679175019264, "std": 0.047742266207933426, "min": -0.1822509765625, "max": 0.134521484375}, "scale_stats": {"shape": [32, 1], "dtype": "torch.float16", "mean": 0.0009164810180664062, "std": 0.00027751922607421875, "min": 0.0004353523254394531, "max": 0.001434326171875}, "zero_point_stats": {"shape": [32, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "loss": 0.000943383201956749, "block_size": 128, "dampening_frac": 0.01}, "timestamp": "<torch.cuda.Event uninitialized>"}, {"layer_name": "model.layers.0.mlp.down_proj", "operation_type": "quantization", "details": {"original_stats": {"shape": [16, 32], "dtype": "torch.float16", "mean": -0.0009317398071289062, "std": 0.020050048828125, "min": -0.05755615234375, "max": 0.058807373046875}, "quantized_stats": {"shape": [16, 32], "dtype": "torch.float16", "mean": -0.000930465292185545, "std": 0.02003958635032177, "min": -0.057342529296875, "max": 0.058563232421875}, "scale_stats": {"shape": [16, 1], "dtype": "torch.float16", "mean": 0.0003657341003417969, "std": 7.063150405883789e-05, "min": 0.0002295970916748047, "max": 0.00046133995056152344}, "zero_point_stats": {"shape": [16, 1], "dtype": "torch.int8", "mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "loss": 1.3710146351542107e-09, "block_size": 128, "dampening_frac": 0.01}, "timestamp": "<torch.cuda.Event uninitialized>"}], "summary": {"total_smoothquant_ops": 7, "total_gptq_ops": 7, "key_findings": ["参数数量变化: 1,091,152 -> 1,097,488"]}}