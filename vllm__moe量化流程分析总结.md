# vLLM MoE 量化实现原理与源码流程详解

本文对 vLLM 中 Mixture-of-Experts（MoE）层的量化实现进行源码级梳理与注释，覆盖：
- 支持的量化方法与选择逻辑
- FusedMoE 模块结构、权重创建与加载规则
- 路由/Top-K 选择、All2All 分发/回收（Prepare/Finalize）与内核执行
- 各量化方法（FP8、INT8、INT4/FP4、WNA16、Marlin/CUTLASS/DeepGemm）的权重/尺度张量形状与数据流
- Expert Parallel（EP）及 all2all 后端（PPLX/DeepEP HT/LL）对接
- 重要限制项（例如 EPLB 仅支持 FP8）与常见陷阱

所有结论均基于当前工作区已安装的 vLLM 源码，文中以“文件路径: 类/函数”给出定位。

---

## 1. 支持的 MoE 量化方法与选择来源

MoE 量化方法由两类入口选择：

- 当使用 Compressed Tensors 量化配置时：
  - 选择函数：`vllm/model_executor/layers/quantization/compressed_tensors/compressed_tensors_moe.py: CompressedTensorsMoEMethod.get_moe_method()`
  - 支持的方法（类名）：
    - `CompressedTensorsW4A4MoeMethod`（FP4/FP4A4，Marlin 或 CUTLASS 路径）
    - `CompressedTensorsW8A8Fp8MoEMethod`（FP8 W8A8，Triton/Marlin/ROCm AITER）
    - `CompressedTensorsW8A8Fp8MoECutlassMethod`（FP8 W8A8，CUTLASS 路径）
    - `CompressedTensorsW8A8Int8MoEMethod`（INT8 W8A8 动态 per-token 激活）
    - `CompressedTensorsWNA16MarlinMoEMethod`（WNA16 Marlin）
    - `CompressedTensorsWNA16MoEMethod`（WNA16 Triton/CUDA kernel）

- 当使用 FP8 原生配置时：
  - 选择函数：`vllm/model_executor/layers/quantization/fp8.py: Fp8Config.get_quant_method()`
  - MoE 对应类：`Fp8MoEMethod`

未配置量化时，采用：
- `vllm/model_executor/layers/fused_moe/layer.py: UnquantizedFusedMoEMethod`

---

## 2. FusedMoE 层结构与初始化流程

入口类：`vllm/model_executor/layers/fused_moe/layer.py: class FusedMoE(torch.nn.Module)`

关键初始化步骤（构造函数 `__init__`）：
- 并行配置汇总：
  - 使用 `FusedMoEParallelConfig.make(...)` 计算 TP/DP/EP 拓扑与是否启用 EP/all2all。
  - 位置：`vllm/model_executor/layers/fused_moe/config.py: FusedMoEParallelConfig.make`
- 构造 MoE 运行时配置 `FusedMoEConfig`：包含专家数、top-k、hidden_dim、并行与量化设置。
  - 位置：`vllm/model_executor/layers/fused_moe/config.py: FusedMoEConfig.make`
- 根据 QuantizationConfig 选择量化方法：
  - 无量化 → `UnquantizedFusedMoEMethod`
  - Compressed Tensors → `CompressedTensorsMoEMethod.get_moe_method`
  - FP8 → `Fp8MoEMethod`
- EPLB（Expert Parallel Load Balancer）限制：仅当量化方法为 `Fp8MoEMethod` 时允许 `enable_eplb=True`。
  - 代码位置：`layer.py: FusedMoE.__init__` 中类型检查并抛出 NotImplementedError 注释
- 调用量化方法 `create_weights(...)` 为本层注册权重参数与量化尺度参数（见第 4 节）。
- 若使用 PPLX/DeepEP-LL 内核，构造分块缓冲 `batched_hidden_states`、`batched_router_logits` 用于分批 all2all。

权重执行入口：
- 推理时通过 `self.quant_method.apply(...)` 实际运行（各量化方法实现见第 6 节）。

---

## 3. MoE Router/Top-K 选择与数据分发

路由入口：`vllm/model_executor/layers/fused_moe/layer.py: FusedMoE.select_experts(...)`

- grouped-topk（DeepSeek 系列）：
  - 实现：`vllm/model_executor/layers/fused_moe/fused_moe.py: grouped_topk(...)`
  - 支持 softmax/sigmoid 打分、分组筛选 topk 的 group-first 策略。
- 标准 fused_topk：
  - 实现：`vllm/model_executor/layers/fused_moe/fused_moe.py: fused_topk(...)`
  - 通过 `ops.topk_softmax` 计算，并可返回 `token_expert_indices`。
- indices dtype 约束：
  - 某些 all2all 实现（PPLX/DeepEP）要求特定索引 dtype。
  - 设置位置：`vllm/model_executor/layers/fused_moe/layer.py: FusedMoEMethodBase.init_prepare_finalize()` 从 PrepareFinalize 查询并保存 `self.topk_indices_dtype`，后续在 `select_experts` 传入。
- EPLB 逻辑（仅 FP8）：
  - `Fp8MoEMethod.apply(...)` 将 `enable_eplb=True` 时额外传入 `expert_load_view`、`logical_to_physical_map`、`logical_replica_count` 至 `FusedMoE.select_experts`。
  - `layer.py` 中 `select_experts` 已暴露 EPLB 相关参数并注释“将逻辑 expert id 映射到物理 expert id 并记录负载”，源码片段显示 EPLB 分支在本文件后续仍有处理（当前工作区文件显示到一半，具体实现不在可见片段中）。参考：`vllm/model_executor/layers/quantization/fp8.py: Fp8MoEMethod.apply()` 的参数传递与 `FusedMoE.__init__` 对 EPLB 的限制说明。

All2All 准备/收尾（Prepare/Finalize）与模块化内核：
- 抽象接口：`vllm/model_executor/layers/fused_moe/modular_kernel.py`
  - `FusedMoEPrepareAndFinalize.prepare/finalize`：负责量化与跨 EP 分发/回收。
  - `FusedMoEPermuteExpertsUnpermute.apply`：专家 GEMM + 激活 + 第二次 GEMM 的中间计算，不负责权重加权与最终聚合。
  - `FusedMoEModularKernel`：将二者组合成统一的前端接口。
- 选择 PrepareFinalize 实现：
  - 位置：`vllm/model_executor/layers/fused_moe/layer.py: FusedMoEMethodBase.init_prepare_finalize()`
  - 三种后端：
    - PPLX：`pplx_prepare_finalize.PplxPrepareAndFinalize`
    - DeepEP High-Throughput：`deepep_ht_prepare_finalize.DeepEPHTPrepareAndFinalize`
    - DeepEP Low-Latency：`deepep_ll_prepare_finalize.DeepEPLLPrepareAndFinalize`
  - 同处根据后端选择专家 GEMM 实现（`select_gemm_impl`），并构造 `FusedMoEModularKernel` 以供量化方法使用。

---

## 4. 权重创建与加载（形状、分片与量化尺度）

通用形状（未说明则以本地 expert 计，`E_local` 表示该 EP rank 上的专家数）：
- `w13_weight`（gate+up 融合，列并行）：形状 `(E_local, 2*I_part, K)`
- `w2_weight`（down，行并行）：形状 `(E_local, K, I_part)`
  - 其中 `K=hidden_size`，`I_part=intermediate_size // tp_size`。

权重创建由各量化方法的 `create_weights(...)` 实现，典型参考：
- 无量化：`layer.py: UnquantizedFusedMoEMethod.create_weights`
- FP8（Compressed Tensors 路径）：
  - `compressed_tensors_moe.py: CompressedTensorsW8A8Fp8MoEMethod.create_weights`
  - `CompressedTensorsW8A8Fp8MoECutlassMethod.create_weights`
- INT8 W8A8：`CompressedTensorsW8A8Int8MoEMethod.create_weights`
- FP4(W4A4)：`CompressedTensorsW4A4MoeMethod.create_weights`（注意 FP4 打包、scale 与 alpha 的额外参数）
- WNA16：
  - Marlin：`CompressedTensorsWNA16MarlinMoEMethod.create_weights`（packed int32 + g_idx 系列张量）
  - Triton/CUDA：`CompressedTensorsWNA16MoEMethod.create_weights`
- 原生 FP8：`quantization/fp8.py: Fp8MoEMethod.create_weights`（支持块量化 block-wise 的 scale_inv 形状）

权重加载与分片规则：
- 统一加载入口：`layer.py: FusedMoE.weight_loader(...)`
  - 处理参数名：`w1/w2/w3`、`...weight`、`...scale/zero/offset`、`...input_scale`、`...g_idx`、`...weight_shape`。
  - 分片维度（TP sharding）：
    - `w1/w3`（MergedColumnParallel）：输出维（即 `2*I_part` 维）按 TP 均分 → `SHARD_ID_TO_SHARDED_DIM["w1"]=0, ["w3"]=0`；再在 w13 的前/后半段分别写入。
    - `w2`（RowParallel）：输入维（`I_part` 维）按 TP 均分 → `SHARD_ID_TO_SHARDED_DIM["w2"]=1`。
  - `is_transposed`：某些方案（如 WNA16）在 `create_weights` 中标注了“转置加载”，`weight_loader` 会对应翻转 shard 维度。
  - 量化尺度加载：依据 `param.quant_method`（tensor/channel/group/block）分派到：
    - `_load_per_tensor_weight_scale` / `_load_per_channel_weight_scale` / `_load_model_weight_or_group_weight_scale`
  - `g_idx`/`*_sort_indices`：用于 actorder 与 Marlin 打包的索引信息。
  - GGUF 相关：若参数带 `is_gguf_weight` 标记，将在 materialize 时考虑合并维度与 TP 分片后形状。
- 特殊权重变换：
  - CompressedTensors WNA16（Marlin/Triton）在加载后进行 repack/permutation：
    - `CompressedTensorsWNA16MarlinMoEMethod.process_weights_after_loading`
    - `CompressedTensorsWNA16MoEMethod.process_weights_after_loading`
  - FP8 权重/尺度归一化（E4M3FN→E4M3FNUZ）：
    - `quantization/fp8.py: normalize_e4m3fn_to_e4m3fnuz` 在 `process_weights_after_loading` 中调用

---

## 5. 模块化内核执行与数据流

标准（非 EP all2all）路径：
- 前端函数：`vllm/model_executor/layers/fused_moe/fused_moe.py: fused_moe(...)`
  1) 计算 topk（`grouped_topk` 或 `fused_topk`）
  2) 调用 `fused_experts(...)` 进行两次 GEMM + 激活 + 聚合
- `fused_experts(...)` 内部：
  - 为不同量化组合（fp8/int8/wna16/fp4）选择执行内核：
    - DeepGemm（fp8，N>512 且平台支持）：`deep_gemm_moe_fp8`
    - CUTLASS BlockScaled Grouped GEMM（fp8，H100+）：`run_cutlass_block_scaled_fused_experts`
    - 否则走 Triton/自定义 CUDA kernel 分块执行：`fused_experts_impl(...)`
  - 关键 Triton kernel：
    - `fused_moe_kernel`（fp8/int8/w8a8 动态/静态尺度、块量化/通道量化等）
    - `fused_moe_kernel_gptq_awq`（wna16/int4&int8 + group_size）
  - 对 token→expert 的对齐与填充：`moe_align_block_size(...)`（按 BLOCK_SIZE_M 对齐，返回排序后的 `sorted_token_ids`、`expert_ids`、`num_tokens_post_padded`）
  - 两次 GEMM 中间态的激活：`torch.ops._C.silu_and_mul` 或 `gelu_and_mul`

EP + all2all（PPLX/DeepEP）路径：
- 量化方法在 `FusedMoEMethodBase.init_prepare_finalize` 中为本层选择：
  - `PrepareFinalize`（负责 prepare/finalize）与专家 GEMM 实现（`TritonExperts`、`BatchedTritonExperts`、`CutlassExpertsFp8`、`TritonOrDeepGemmExperts` 等）
  - 组合为 `FusedMoEModularKernel`，量化方法 `apply(...)` 直接调用该模块。
- 数据流（见 `modular_kernel.py: FusedMoEModularKernel.forward`)：
  1) `prepare_finalize.prepare(...)`：可量化+分发；返回量化后的激活、可选的专家 token 数、以及可能重排过的 topk ids/weights
  2) `experts.apply(...)`：执行专家 GEMM+激活+第二次 GEMM，写入未加权未规约的输出
  3) `prepare_finalize.finalize(...)`：在输出上应用路由权重并最终规约到 (M,K)

---

## 6. 各量化方法实现要点

以下仅列出关键点与张量/尺度形状，详细请参考对应类的 `create_weights/process_weights_after_loading/apply/select_gemm_impl`。

### 6.1 UnquantizedFusedMoEMethod
- 文件：`fused_moe/layer.py: UnquantizedFusedMoEMethod`
- 权重创建：`w13_weight: (E_local, 2*I_part, K)`, `w2_weight: (E_local, K, I_part)`
- 运行：
  - CUDA：`fused_experts(...)`（Triton 内核）
  - ROCm AITER：`rocm_aiter_fused_experts`
  - CPU：`cpu_fused_moe`（IPEX 或 SGL）
  - TPU：`moe_pallas.fused_moe`
- 限制：`enable_eplb` 未支持（构造/调用时直接报错）。

### 6.2 CompressedTensorsW8A8Fp8MoEMethod
- 文件：`compressed_tensors/compressed_tensors_moe.py`
- 要求：权重 per-tensor 或 per-channel（通道×token 动态）量化，激活可静态（per-tensor）或动态（per-token）。
- 权重与尺度：
  - `w13_weight/w2_weight: float8_e4m3fn`
  - per-tensor：`w13_weight_scale: (E_local, 2) → 合并为 (E_local,)`；`w2_weight_scale: (E_local,)`
  - per-channel：`w13_weight_scale: (E_local, 2*I_part, 1)`；`w2_weight_scale: (E_local, K, 1)`
  - 激活静态：`w13_input_scale: (E_local,)`，`w2_input_scale: (E_local,)`（最终取 max）
- 处理：
  - 若 FNUZ：归一化权重与尺度（`normalize_e4m3fn_to_e4m3fnuz`）
  - per-tensor w13 合并：对 gate/up 两段分别反量化→以最大 scale 重量化，使每 expert 仅一条 w13 scale
  - ROCm AITER：权重布局 `shuffle_weights`
- 运行：
  - ROCm AITER 或 Marlin 优先；否则 `fused_experts(..., use_fp8_w8a8=True, per_channel_quant=...)`
  - all2all：通过 `select_gemm_impl` 选择 `TritonExperts`/`BatchedTritonExperts`，并使用模块化内核。

### 6.3 CompressedTensorsW8A8Fp8MoECutlassMethod
- 文件：同上
- 内核：`cutlass_moe_fp8`
- Expert map 支持：当 `num_dispatchers>1` 或 `experts.supports_expert_map()` 为假时禁用（`disable_expert_map=True`）。

### 6.4 CompressedTensorsW8A8Int8MoEMethod
- 文件：同上
- 要求：权重 per-channel，激活 per-token 动态
- 权重：`w13/w2: int8`；scale 分别为 `(E_local, 2*I_part, 1)` 与 `(E_local, K, 1)`
- 运行：`fused_experts(..., use_int8_w8a8=True, per_channel_quant=True)`

### 6.5 CompressedTensorsW4A4MoeMethod（FP4）
- 文件：同上
- 打包：权重以 `uint8`/半宽存储；group_size=16；附带 `blockscale_swizzled` 与 `g*_alphas`（由输入/权重全局尺度计算）
- 内核：Marlin（优先）或 CUTLASS（`cutlass_moe_fp4`）
- 限制：仅 SiLU 激活；不支持 `apply_router_weight_on_input`；当前不支持 expert_map

### 6.6 CompressedTensorsWNA16MarlinMoEMethod / CompressedTensorsWNA16MoEMethod
- 文件：同上
- 打包：`int32` 存 packed 权重，含 `g_idx` 相关参数与 `*_weight_shape` 辅助信息
- Marlin 版 `process_weights_after_loading`：
  - `ops.gptq_marlin_moe_repack` 重排 qweight
  - `marlin_moe_permute_scales` 重排 scales
  - 构造 `workspace`，并在 `apply` 走 `torch.ops.vllm.fused_marlin_moe`
- Triton/CUDA 版：
  - 将 packed 权重转置并 `view` 为 `uint8`，scales 维度转置匹配内核
  - 运行 `fused_experts(..., use_int4_w4a16/use_int8_w8a16, block_shape=[0, group_size])`

### 6.7 Fp8MoEMethod（原生 FP8 配置）
- 文件：`quantization/fp8.py: Fp8MoEMethod`
- 支持：
  - 权重 FP8（可 block-wise），激活静态/动态
  - H100+ CUTLASS BlockScaled Grouped GEMM 与 Ada/Hopper DeepGemm（在满足条件时启用）
- 权重/尺度：
  - 非块量化：`w13_weight_scale: (E_local, 2) → 合并为 (E_local,)`、`w2_weight_scale: (E_local,)`
  - 块量化（weight_block_size=[block_n, block_k]）：
    - `w13_weight_scale_inv: (E_local, 2*ceil(I_part/block_n), ceil(K/block_k))`
    - `w2_weight_scale_inv: (E_local, ceil(K/block_n), ceil(I_part/block_k))`
- 处理：
  - FNUZ 归一化；若非 fp8 checkpoint，直接在加载后就地量化（`ops.scaled_fp8_quant`）
  - 合并 w13 两段 scale 为每 expert 单一 scale（同 CT-FP8）
  - ROCm AITER：`shuffle_weights`
- 运行：
  - 非 all2all：`self.fused_experts = functools.partial(fused_experts, use_fp8_w8a8=True, block_shape=..., allow_deep_gemm=..., allow_cutlass_block_scaled_grouped_gemm=...)`
  - all2all：`select_gemm_impl` 选择 `TritonOrDeepGemmExperts/BatchedTritonOrDeepGemmExperts`
  - EPLB：唯一支持的量化方法；`apply(...)` 将 EPLB 所需张量传入 `FusedMoE.select_experts`。

---

## 7. 参数与张量形状总览（常见）

- 输入/路由：
  - `hidden_states`: (M, K)
  - `router_logits`: (M, E_global)（逻辑专家数）
  - `topk_weights/topk_ids`: (M, top_k)
- 权重（本地专家）：
  - `w13_weight`: (E_local, 2*I_part, K)
  - `w2_weight`: (E_local, K, I_part)
- 量化尺度（示例）：
  - FP8 per-tensor：
    - `w13_weight_scale`: (E_local,)；`w2_weight_scale`: (E_local,)
    - `w13_input_scale/w2_input_scale`: () 或 (E_local,)（最后取 max → 标量）
  - FP8 block-wise：
    - `w13_weight_scale_inv`: (E_local, 2*ceil(I_part/bn), ceil(K/bk))
    - `w2_weight_scale_inv`: (E_local, ceil(K/bn), ceil(I_part/bk))
  - INT8 per-channel：
    - `w13_weight_scale`: (E_local, 2*I_part, 1)
    - `w2_weight_scale`: (E_local, K, 1)
  - WNA16（group）：
    - `weight_scale` 形状依赖 group_size，通常为 `(E_local, num_groups, N)` 或转置版本（见对应方法）

---

## 8. 并行与 all2all 后端

- 并行配置：`fused_moe/config.py: FusedMoEParallelConfig` 与 `FusedMoEConfig`
  - `use_ep` 条件：`dp_size * tp_size > 1` 且并行配置开启 EP
  - all2all 内核选择：环境变量 `VLLM_ALL2ALL_BACKEND` 为 `pplx`/`deepep_high_throughput`/`deepep_low_latency`
- `FusedMoEMethodBase.init_prepare_finalize`：
  - PPLX：构造 handle 并创建 `PplxPrepareAndFinalize`（可能需提供 group_name）
  - DeepEP-HT：`DeepEPHTPrepareAndFinalize`
  - DeepEP-LL：`DeepEPLLPrepareAndFinalize`（可选择 FP8 dispatch 条件，注释称默认关闭）

---

## 9. 重要限制与注意事项

- EPLB 仅支持 FP8：
  - `layer.py: FusedMoE.__init__` 中显式检查：非 `Fp8MoEMethod` 时设置 `enable_eplb=True` 会抛出 NotImplementedError。
- 激活函数限制：
  - 多数内核仅支持 `silu`（尤其是 Marlin/CUTLASS/FUSED 路径）；部分路径支持 `gelu`（Triton fused_moe）
- `apply_router_weight_on_input` 限制：
  - FP4/W4A4 不支持；其它路径也有条件限制（如 DeepGemm/CUTLASS 路径要求 False）
- expert_map 支持：
  - 某些实现会禁用（如 `CompressedTensorsW8A8Fp8MoECutlassMethod` 的 `disable_expert_map`）
- CPU/TPU 后端：
  - CPU 仅支持 x86（`IPEX`/`SGL`），TPU 走 `moe_pallas`
- ROCm AITER：
  - 需要特定平台/环境开关；权重需 `shuffle_weights` 重排

---

## 10. 端到端运行主干流程（伪代码说明）

以 FP8（原生）+ EP + DeepEP-LL 为例，主干调用链如下：

1) 实例化 FusedMoE：`FusedMoE.__init__` → `FusedMoEConfig.make` → 选择 `Fp8MoEMethod` → `Fp8MoEMethod.create_weights` 注册权重与尺度参数
2) 初始化 all2all：`Fp8MoEMethodBase.init_prepare_finalize` → 选择 `DeepEPLLPrepareAndFinalize` + `TritonOrDeepGemmExperts` → 组装 `FusedMoEModularKernel`
3) 前向：`Fp8MoEMethod.apply(...)`
   - `FusedMoE.select_experts(...)`（可启用 EPLB → 逻辑→物理专家映射与负载记录）
   - 调用 `FusedMoEModularKernel.forward`：
     - `prepare_finalize.prepare(...)`：量化/分发，返回 `a1q`、可选 `expert_num_tokens`、可能重排的 `topk_ids/weights`
     - `experts.apply(...)`：执行两次 GEMM 与激活，生成未加权、未规约的输出
     - `prepare_finalize.finalize(...)`：应用 top-k 权重并规约到最终输出 `(M,K)`

无 EP/all2all 时：
- 由 `fused_moe.fused_moe → fused_experts → fused_experts_impl` 执行 Triton/CUDA/CUTLASS/DeepGemm 路径。

---

## 11. 源码关键位置索引（按主题）

- FusedMoE 主体与通用逻辑：
  - `vllm/model_executor/layers/fused_moe/layer.py`: `FusedMoE`, `FusedMoEMethodBase`, `UnquantizedFusedMoEMethod`, `determine_expert_map`, `FusedMoE.weight_loader`, `FusedMoE.select_experts`
- 并行与量化配置：
  - `vllm/model_executor/layers/fused_moe/config.py`: `FusedMoEConfig`, `FusedMoEParallelConfig`, `FusedMoEQuantConfig`
- 模块化内核接口：
  - `vllm/model_executor/layers/fused_moe/modular_kernel.py`: `FusedMoEPrepareAndFinalize`, `FusedMoEPermuteExpertsUnpermute`, `FusedMoEModularKernel`
- Triton/CUDA/CUTLASS/DeepGemm 内核与路由：
  - `vllm/model_executor/layers/fused_moe/fused_moe.py`: `grouped_topk`, `fused_topk`, `fused_experts`, `fused_experts_impl`, `invoke_fused_moe_kernel`, `fused_moe_kernel(_gptq_awq)`
- all2all 后端 Prepare/Finalize：
  - `vllm/model_executor/layers/fused_moe/pplx_prepare_finalize.py`
  - `vllm/model_executor/layers/fused_moe/deepep_ht_prepare_finalize.py`
  - `vllm/model_executor/layers/fused_moe/deepep_ll_prepare_finalize.py`
- Compressed Tensors MoE 量化方法：
  - `vllm/model_executor/layers/quantization/compressed_tensors/compressed_tensors_moe.py`
- 原生 FP8 量化方法（含 Linear/KV）：
  - `vllm/model_executor/layers/quantization/fp8.py`: `Fp8MoEMethod`, `Fp8LinearMethod`, `Fp8KVCacheMethod`

---

## 12. 小结

- vLLM 的 MoE 量化采用“方法类 + 模块化内核（Prepare/Experts/Finalize）”的分层设计，既支持单机/非 EP 的 Triton/CUDA 路径，也能在 EP 下无缝切换 PPLX/DeepEP 等 all2all 后端。
- 权重与尺度的形状、加载与分片规则在 `FusedMoE.weight_loader` 中统一处理，各量化方法通过设置 `quant_method` 与 `is_transposed/load_full_w2` 等属性实现自定义装载。
- FP8、INT8、WNA16、FP4 等多种方案共存：
  - FP8（含 block-wise）支持 CUTLASS BlockScaled Grouped GEMM 与 DeepGemm；
  - WNA16/FP4 等权重需 repack/scale 重排并走专用内核；
  - INT8 W8A8 要求 per-channel 权重量化与 per-token 动态激活尺度。
- EPLB 当前仅在 FP8 路径实现（构造与选择专家时生效）。

如需进一步生成数据流图或针对特定模型（如 DeepSeek/Mixtral）出具具体张量尺寸与内核调用路径，可在上述位置基础上按模型配置补充。

---

## 13. EPLB（Expert Parallel Load Balancer）详细流程（源码指引）

启用位置与限制：
- 仅 FP8 路径支持：`quantization/fp8.py: Fp8MoEMethod` 在 `FusedMoE.__init__` 检查非 FP8 则报错（不支持）。
- 传参与启用点：`Fp8MoEMethod.apply(...)` 将 `enable_eplb=True` 时，连同
  `expert_load_view`、`logical_to_physical_map`、`logical_replica_count` 传入 `FusedMoE.select_experts(...)`。

核心选择与记载逻辑：`fused_moe/layer.py: FusedMoE.select_experts(...)`
- 正常 topk 选择后（`grouped_topk` 或 `fused_topk`），若 `enable_eplb`：
  1) 逻辑 expert → 物理 expert 映射：
     - 为每个逻辑 expert id 随机选择一个副本索引 `replica_indices`，
       用 `logical_to_physical_map.gather(...)` 得到 `physical_ids` 作为新的 `topk_ids`。
  2) 记录负载：
     - 若存在 `expert_map`（EP 下本 rank 的本地专家映射），先将全局物理 id 映射到本地 id；
     - 将无效 id（非本地专家）置 0 并用布尔 mask 作为 `src`，调用 `expert_load_view.scatter_add_(dim=0, index, src)` 高效统计各本地专家接收的 token 数；
     - 最终把 `topk_ids` 转回 `indices_type`（由 Prepare/Finalize 指定，如 PPLX `uint32`、DeepEP `int64`）。

注意：EPLB 仅改变专家 id 的选择与统计，不影响后续 Experts/GEMM 的数据流；与 all2all/EP 的 expert_map 机制兼容。

---

## 14. 运行时环境变量与性能开关（基于源码出现处）

以下变量在本工作区 vLLM 源码中被 MoE/量化路径直接使用：

- VLLM_MOE_DP_CHUNK_SIZE
  - 位置：`fused_moe/layer.py: FusedMoE.__init__` → 作为 `FusedMoEConfig.max_num_tokens`（DP 分块上限）
  - 影响：PPLX/DeepEP-LL 分批 all2all 的分块大小；非 all2all 时也用于 chunked forward 上限

- VLLM_FUSED_MOE_CHUNK_SIZE
  - 位置：`fused_moe/fused_moe.py: fused_experts_impl`；`modular_kernel.py: FusedMoEPermuteExpertsUnpermute.enable_chunking`
  - 影响：Triton/CUDA fused_moe 的 token 分块执行规模；配合是否启用 activation chunking

- VLLM_ENABLE_FUSED_MOE_ACTIVATION_CHUNKING
  - 位置：`modular_kernel.py: FusedMoEPermuteExpertsUnpermute.enable_chunking`
  - 影响：是否允许模块化内核进行激活分块（由 Experts implementation 决定是否支持）

- VLLM_TEST_FORCE_FP8_MARLIN
  - 位置：`compressed_tensors_moe.py: CompressedTensorsW8A8Fp8MoEMethod.__init__`；`quantization/fp8.py: Fp8MoEMethod/Fp8LinearMethod`
  - 影响：在缺少原生 FP8 硬件（或强制）时，改走 Marlin 快速权重路径（禁用激活量化）

- VLLM_USE_DEEP_GEMM
  - 位置：`quantization/fp8.py: Fp8MoEMethod.__init__`
  - 影响：Hopper（SM90）且 block-quant 条件满足时启用 DeepGemm MoE 内核

- VLLM_ROCM_MOE_PADDING / VLLM_ROCM_FP8_PADDING
  - 位置：`fused_moe/layer.py: UnquantizedFusedMoEMethod._maybe_pad_weight`；`quantization/fp8.py: Fp8LinearMethod._maybe_pad_weight`
  - 影响：ROCm 下为提升访存性能，对权重张量进行对齐 padding

- VLLM_CPU_SGL_KERNEL
  - 位置：`fused_moe/layer.py: UnquantizedFusedMoEMethod.process_weights_after_loading`
  - 影响：x86 CPU + AMX tile + BF16 时，选择 SGL 路径并对权重进行打包

- VLLM_ROCM_USE_AITER / VLLM_ROCM_USE_AITER_LINEAR
  - 位置：`quantization/fp8.py: Fp8LinearMethod.__init__`
  - 影响：ROCm 平台启用 AITER 线性/（MoE 侧通过 `is_rocm_aiter_moe_enabled()` 检测）

提示：all2all 后端选择在更上层配置/构建阶段决定（PPLX/DeepEP 可用性通过 `has_pplx()/has_deep_ep()` 与分发器句柄获取判断），非直接以某个单一环境变量切换。

---

## 15. `FusedMoE.weight_loader` 字段对照与装载规则（关键分支）

入口：`fused_moe/layer.py: FusedMoE.weight_loader(...)`

- 通用约束与分片：
  - 仅接受 `shard_id in {"w1","w2","w3"}`；`is_transposed` 为 True 时互换 shard 维度
  - TP 分片维映射：`SHARD_ID_TO_SHARDED_DIM = {"w1":0, "w2":1, "w3":0}`
  - w1/w3 合并至 `w13`：先按 shard 维度二分，前半写入 w1，后半写入 w3

- 模式分支：
  - `"input_scale" in weight_name`：
    - 加载至对应 expert 的输入尺度；Compressed Tensors 路径会检查 w1/w3 的 input_scale 一致性
  - `"g_idx" in weight_name`：
    - 调用 `_load_g_idx(...)`；w2 经 `_load_w2`（按输入维分片），w1/w3 直接拷贝
    - 相关排序索引 `*_g_idx_sort_indices` 在 WNA16/Marlin 处理中使用
  - `"ModelOpt" in quant_method_name`：
    - 特殊路径：`weight_scale_2`/`input_scale` 按 per-tensor 规则；`weight` 按模型/分组尺度分支
  - `"scale"|"zero"|"offset" in weight_name`：
    - 根据 `param.quant_method` 选择：
      - TENSOR → `_load_per_tensor_weight_scale`
      - CHANNEL → `_load_per_channel_weight_scale`
      - GROUP/BLOCK → `_load_model_weight_or_group_weight_scale`（支持 `load_full_w2`）
  - `"weight_shape" in weight_name`：
    - 记录 packed 权重的形状信息（WNA16 等）
  - `"weight" in weight_name`：
    - 加载实际权重：w2 通过 `_load_w2(...)`；w1/w3 通过 `_load_w13(...)`（对 w13 的两半分别写入）

- 额外：
  - GGUF 权重：`is_gguf_weight`/`UninitializedParameter` 路径按最终形状 materialize 后再分片写入
  - CompressedTensors WNA16：权重加载后在 `process_weights_after_loading` 阶段进行 repack/permute（Marlin/Triton 版本不同）

---

## 16. 内核配置选择路径（尺寸 → 配置）

- 计算入口：`fused_moe/fused_moe.py: try_get_optimal_moe_config(...)`
  - 优先按 `(E, N, dtype, block_shape, device_name)` 拼接出的文件名（`get_config_file_name`）加载 `configs/*.json`
  - 否则退化到 `get_default_config(...)`：按 `M、E、N、K、topk、dtype、block_shape` 选择默认 BLOCK_SIZE_{M,N,K}、warps/stages
- WNA16（int4/int8）路径：
  - 若 `block_shape[1] > 0` 进入 GPTQ/AWQ 专用 kernel 分支：
    - `get_moe_wna16_block_config(...)` 根据 `num_valid_tokens、group_size、num_experts、bit` 判断是否走 CUDA 专用核并给出 BLOCK_SIZE 组合
- 模块化 all2all：
  - `modular_kernel.TritonExperts.apply(...)` 与 `FusedMoEModularKernel.forward(...)` 内也会按批次/分块动态获取配置与工作区大小

---

## 17. 简化数据流图（Router → Prepare → Experts → Finalize）

```mermaid
flowchart LR
  A[hidden_states (M,K)\nrouter_logits (M,E)] --> B{top-k 选择\n(grouped_topk / fused_topk)}
  B -->|topk_ids, topk_weights| C[Prepare\n量化 + all2all 分发\n(PPLX/DeepEP/No-EP)]
  C -->|a1q, (a1q_scale), expert_num_tokens| D[Experts.apply\nGEMM(w1) → 激活 → GEMM(w2)\n(Triton/CUDA/Marlin/CUTLASS/DeepGemm)]
  D --> E[Finalize\n加权 + 规约/回收\n(out: M,K)]
  subgraph 可选: EPLB
    B -->|逻辑→物理映射、负载统计| B
  end
```

说明：
- 有 EP/all2all 时，Prepare/Finalize 由 `PplxPrepareAndFinalize`、`DeepEPHT/LLPrepareAndFinalize` 实现；无 EP 则在本地执行对齐与内核。
- 内核选择与配置见上节；DeepGemm/CUTLASS 具平台与数据类型约束。

---

上述补充覆盖：EPLB 明细、环境变量与性能开关、weight_loader 对照、尺寸到配置路径，以及端到端时序图，便于在不同模型/平台下定位性能与正确性问题。
