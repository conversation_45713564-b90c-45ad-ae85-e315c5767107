#!/usr/bin/env python3
"""
Debug脚本：验证W8A8配置如何区别应用到权重和激活量化
追踪配置解析、应用和执行过程
"""

import torch
import json
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

class W8A8ConfigTracker:
    """W8A8配置追踪器"""
    
    def __init__(self):
        self.config_applications = []
        self.quantization_events = []
        
    def track_config_application(self, module_name, quantization_scheme):
        """追踪配置应用"""
        config_info = {
            'module_name': module_name,
            'has_weights_config': hasattr(quantization_scheme, 'weights') and quantization_scheme.weights is not None,
            'has_activation_config': hasattr(quantization_scheme, 'input_activations') and quantization_scheme.input_activations is not None,
            'weights_config': None,
            'activation_config': None
        }
        
        if config_info['has_weights_config']:
            weights = quantization_scheme.weights
            config_info['weights_config'] = {
                'num_bits': weights.num_bits,
                'dynamic': weights.dynamic,
                'strategy': weights.strategy,
                'symmetric': weights.symmetric,
                'observer': weights.observer,
                'type': weights.type
            }
        
        if config_info['has_activation_config']:
            activations = quantization_scheme.input_activations
            config_info['activation_config'] = {
                'num_bits': activations.num_bits,
                'dynamic': activations.dynamic,
                'strategy': activations.strategy,
                'symmetric': activations.symmetric,
                'observer': activations.observer,
                'type': activations.type
            }
        
        self.config_applications.append(config_info)
        return config_info

def patch_quantization_mixin():
    """给QuantizationMixin打补丁以追踪配置应用"""
    from compressed_tensors.quantization import QuantizationMixin
    
    original_initialize = QuantizationMixin.initialize_quantization
    
    def tracked_initialize_quantization(self, model):
        print("🔧 QuantizationMixin.initialize_quantization 开始执行")
        print(f"   位置: compressed_tensors.quantization.QuantizationMixin")
        
        # 执行原始初始化
        result = original_initialize(self, model)
        
        # 追踪配置应用
        tracker = W8A8ConfigTracker()
        
        print(f"\n📊 检查模型中的量化配置应用:")
        for name, module in model.named_modules():
            if hasattr(module, 'quantization_scheme'):
                config_info = tracker.track_config_application(name, module.quantization_scheme)
                
                print(f"\n🔍 模块: {name}")
                print(f"   模块类型: {type(module).__name__}")
                print(f"   有权重配置: {config_info['has_weights_config']}")
                print(f"   有激活配置: {config_info['has_activation_config']}")
                
                if config_info['weights_config']:
                    wc = config_info['weights_config']
                    print(f"   权重配置: bits={wc['num_bits']}, dynamic={wc['dynamic']}, "
                          f"strategy={wc['strategy']}, observer={wc['observer']}")
                
                if config_info['activation_config']:
                    ac = config_info['activation_config']
                    print(f"   激活配置: bits={ac['num_bits']}, dynamic={ac['dynamic']}, "
                          f"strategy={ac['strategy']}, observer={ac['observer']}")
        
        # 保存追踪结果
        global global_tracker
        global_tracker = tracker
        
        return result
    
    QuantizationMixin.initialize_quantization = tracked_initialize_quantization

def analyze_w8a8_recipe():
    """分析W8A8 recipe配置"""
    print("🔍 分析W8A8 Recipe配置")
    print("="*80)
    
    # 创建W8A8配置
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    print(f"📋 Recipe组成:")
    for i, modifier in enumerate(recipe, 1):
        print(f"   {i}. {type(modifier).__name__}")
        
        if hasattr(modifier, 'scheme'):
            print(f"      scheme: {modifier.scheme}")
        if hasattr(modifier, 'targets'):
            print(f"      targets: {modifier.targets}")
        if hasattr(modifier, 'ignore'):
            print(f"      ignore: {modifier.ignore}")
    
    # 解析GPTQ配置
    gptq_modifier = recipe[1]
    if hasattr(gptq_modifier, 'resolve_quantization_config'):
        print(f"\n🔧 解析GPTQ量化配置:")
        try:
            config = gptq_modifier.resolve_quantization_config()
            print(f"   配置类型: {type(config).__name__}")
            
            if hasattr(config, 'config_groups'):
                print(f"   配置组数量: {len(config.config_groups)}")
                
                for group_name, scheme in config.config_groups.items():
                    print(f"\n   📊 配置组: {group_name}")
                    print(f"      目标层: {scheme.targets}")
                    
                    if scheme.weights:
                        print(f"      权重配置:")
                        print(f"        bits: {scheme.weights.num_bits}")
                        print(f"        dynamic: {scheme.weights.dynamic}")
                        print(f"        strategy: {scheme.weights.strategy}")
                        print(f"        observer: {scheme.weights.observer}")
                        print(f"        symmetric: {scheme.weights.symmetric}")
                    
                    if scheme.input_activations:
                        print(f"      激活配置:")
                        print(f"        bits: {scheme.input_activations.num_bits}")
                        print(f"        dynamic: {scheme.input_activations.dynamic}")
                        print(f"        strategy: {scheme.input_activations.strategy}")
                        print(f"        observer: {scheme.input_activations.observer}")
                        print(f"        symmetric: {scheme.input_activations.symmetric}")
        
        except Exception as e:
            print(f"   ❌ 配置解析失败: {e}")
    
    return recipe

def simulate_quantization_process():
    """模拟量化过程"""
    print("\n🚀 模拟量化过程")
    print("="*80)
    
    # 打补丁
    patch_quantization_mixin()
    
    # 加载模型
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(MODEL_ID, torch_dtype=torch.float16)
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 准备数据
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(8))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=128, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 分析recipe
    recipe = analyze_w8a8_recipe()
    
    # 执行量化
    print(f"\n🔄 执行oneshot量化...")
    from llmcompressor.entrypoints.oneshot import oneshot
    
    try:
        quantized_model = oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=128,
            num_calibration_samples=8,
        )
        
        print(f"✅ 量化成功完成!")
        
        # 分析量化后的模型
        analyze_quantized_model(quantized_model)
        
    except Exception as e:
        print(f"❌ 量化失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_quantized_model(model):
    """分析量化后的模型"""
    print(f"\n📊 分析量化后的模型")
    print("="*80)
    
    quantized_layers = []
    
    for name, module in model.named_modules():
        if hasattr(module, 'quantization_scheme'):
            layer_info = {
                'name': name,
                'type': type(module).__name__,
                'has_quantization_scheme': True,
                'has_weight_scale': hasattr(module, 'weight_scale'),
                'has_weight_zero_point': hasattr(module, 'weight_zero_point'),
                'weight_dtype': str(module.weight.dtype) if hasattr(module, 'weight') else None,
                'weight_shape': list(module.weight.shape) if hasattr(module, 'weight') else None
            }
            
            if hasattr(module, 'weight_scale'):
                layer_info['weight_scale_shape'] = list(module.weight_scale.shape)
                layer_info['weight_scale_dtype'] = str(module.weight_scale.dtype)
            
            if hasattr(module, 'weight_zero_point'):
                layer_info['weight_zero_point_shape'] = list(module.weight_zero_point.shape)
                layer_info['weight_zero_point_dtype'] = str(module.weight_zero_point.dtype)
            
            quantized_layers.append(layer_info)
    
    print(f"🔍 发现 {len(quantized_layers)} 个量化层:")
    for layer in quantized_layers:
        print(f"\n📍 {layer['name']} ({layer['type']}):")
        print(f"   权重形状: {layer['weight_shape']}")
        print(f"   权重类型: {layer['weight_dtype']}")
        print(f"   有weight_scale: {layer['has_weight_scale']}")
        print(f"   有weight_zero_point: {layer['has_weight_zero_point']}")
        
        if layer['has_weight_scale']:
            print(f"   scale形状: {layer['weight_scale_shape']}")
            print(f"   scale类型: {layer['weight_scale_dtype']}")
        
        if layer['has_weight_zero_point']:
            print(f"   zero_point形状: {layer['weight_zero_point_shape']}")
            print(f"   zero_point类型: {layer['weight_zero_point_dtype']}")

def compare_config_vs_implementation():
    """对比配置与实际实现"""
    print(f"\n🔍 对比配置与实际实现")
    print("="*80)
    
    print(f"📋 理论配置 vs 实际实现:")
    
    print(f"\n🔧 权重量化 (W8):")
    print(f"   配置: dynamic=false, strategy=channel, observer=minmax")
    print(f"   实现: GPTQ阶段静态量化，每个输出通道独立scale")
    print(f"   存储: weight_scale + weight_zero_point参数")
    print(f"   时机: 校准时一次性计算")
    
    print(f"\n🔧 激活量化 (A8):")
    print(f"   配置: dynamic=true, strategy=token, observer=null")
    print(f"   实现: 推理时动态量化，每个token独立scale")
    print(f"   存储: 无需存储scale参数")
    print(f"   时机: 推理时实时计算")
    
    print(f"\n🎯 关键差异:")
    print(f"   1. 权重量化在llmcompressor中完成，激活量化在推理引擎中完成")
    print(f"   2. 权重scale需要存储，激活scale实时计算")
    print(f"   3. 权重使用Observer，激活直接计算abs_max")
    print(f"   4. 权重Channel-wise，激活Per-token")

def main():
    """主函数"""
    print("🔍 W8A8配置中权重与激活量化的区别处理")
    print("="*80)
    
    # 模拟量化过程
    simulate_quantization_process()
    
    # 对比分析
    compare_config_vs_implementation()
    
    print(f"\n🎉 分析完成!")
    print("="*80)
    print("关键发现:")
    print("1. W8A8通过不同的配置参数区别处理权重和激活")
    print("2. 权重量化在校准时完成，激活量化在推理时完成")
    print("3. 配置参数直接影响量化策略和执行时机")
    print("4. SmoothQuant预处理为后续量化创造更好条件")

if __name__ == "__main__":
    main()
