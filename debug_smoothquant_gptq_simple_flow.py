#!/usr/bin/env python3
"""
简化版Debug脚本：追踪SmoothQuant + GPTQ的关键流程
重点分析scale处理和per-channel vs per-block的关系
"""

import torch
import time
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset
from llmcompressor.modifiers.quantization import GPTQModifier
from llmcompressor.modifiers.smoothquant import SmoothQuantModifier

class SimpleFlowTracker:
    """简化流程追踪器"""
    
    def __init__(self):
        self.events = []
        
    def log_event(self, event_type, layer_name, data):
        """记录事件"""
        self.events.append({
            'type': event_type,
            'layer_name': layer_name,
            'data': data,
            'timestamp': time.time()
        })
        
        # 实时打印关键事件
        if event_type in ['smoothquant_applied', 'gptq_quantized', 'scale_calculated']:
            print(f"📍 {event_type}: {layer_name}")
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, (int, float)):
                        print(f"   {key}: {value:.6f}")
                    elif isinstance(value, list) and len(value) == 2:
                        print(f"   {key}: [{value[0]:.6f}, {value[1]:.6f}]")
                    else:
                        print(f"   {key}: {value}")

def patch_for_simple_tracking():
    """简单追踪补丁"""
    global tracker
    tracker = SimpleFlowTracker()
    
    # 1. 追踪GPTQ量化
    from llmcompressor.modifiers.quantization.gptq.gptq_quantize import quantize_weight
    original_quantize_weight = quantize_weight
    
    def tracked_quantize_weight(module, quant_args, hessians_dict, blocksize, percdamp):
        # 获取模块名称
        module_name = getattr(module, '_debug_name', f'unknown_{type(module).__name__}')
        
        print(f"\n🔧 GPTQ量化: {module_name}")
        print(f"   权重形状: {module.weight.shape}")
        print(f"   量化策略: {quant_args.strategy}")
        print(f"   块大小: {blocksize}")
        
        # 记录量化前状态
        pre_stats = {
            'weight_shape': list(module.weight.shape),
            'weight_mean': module.weight.mean().item(),
            'weight_std': module.weight.std().item(),
            'weight_range': [module.weight.min().item(), module.weight.max().item()],
            'block_size': blocksize,
            'strategy': str(quant_args.strategy)
        }
        
        tracker.log_event('gptq_pre_quantization', module_name, pre_stats)
        
        # 执行量化
        loss, quantized_weight, scale, zero_point, g_idx = original_quantize_weight(
            module, quant_args, hessians_dict, blocksize, percdamp
        )
        
        # 记录量化后状态
        post_stats = {
            'quantized_weight_shape': list(quantized_weight.shape),
            'scale_shape': list(scale.shape),
            'scale_range': [scale.min().item(), scale.max().item()],
            'scale_mean': scale.mean().item(),
            'scale_std': scale.std().item(),
            'zero_point_shape': list(zero_point.shape),
            'zero_point_range': [zero_point.min().item(), zero_point.max().item()],
            'quantization_loss': loss
        }
        
        tracker.log_event('gptq_quantized', module_name, post_stats)
        
        return loss, quantized_weight, scale, zero_point, g_idx
    
    # 2. 追踪Observer的scale计算
    from llmcompressor.observers.base import Observer
    original_observer_call = Observer.__call__
    
    def tracked_observer_call(self, tensor, g_idx=None):
        print(f"\n🔍 Observer计算scale:")
        print(f"   Observer类型: {type(self).__name__}")
        print(f"   输入张量形状: {tensor.shape}")
        print(f"   量化策略: {self.quantization_args.strategy}")
        
        # 执行原始计算
        scale, zero_point = original_observer_call(self, tensor, g_idx)
        
        # 记录结果
        observer_stats = {
            'observer_type': type(self).__name__,
            'input_shape': list(tensor.shape),
            'scale_shape': list(scale.shape),
            'scale_range': [scale.min().item(), scale.max().item()],
            'scale_mean': scale.mean().item(),
            'zero_point_shape': list(zero_point.shape),
            'strategy': str(self.quantization_args.strategy)
        }
        
        tracker.log_event('scale_calculated', 'observer', observer_stats)
        
        return scale, zero_point
    
    # 应用补丁
    import llmcompressor.modifiers.quantization.gptq.gptq_quantize as gptq_module
    gptq_module.quantize_weight = tracked_quantize_weight
    Observer.__call__ = tracked_observer_call
    
    print("✅ 简单追踪补丁已应用")

def add_debug_names_to_modules(model):
    """为模块添加debug名称"""
    for name, module in model.named_modules():
        module._debug_name = name

def run_simple_flow_debug():
    """运行简化流程debug"""
    print("🚀 开始简化流程debug")
    print("="*80)
    
    # 应用追踪补丁
    patch_for_simple_tracking()
    
    # 加载模型
    MODEL_ID = "/home/<USER>/single_llama"
    model = AutoModelForCausalLM.from_pretrained(MODEL_ID, torch_dtype=torch.float16)
    tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)
    
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        model.resize_token_embeddings(len(tokenizer))
    
    # 添加debug名称
    add_debug_names_to_modules(model)
    
    # 准备数据
    ds = load_dataset("/workspace/dataset/datasets--HuggingFaceH4--ultrachat_200k/snapshots/8049631c405ae6576f93f445c6b8166f76f5505a/", split="train_sft")
    ds = ds.shuffle(seed=42).select(range(8))
    
    def preprocess(example):
        return {"text": tokenizer.apply_chat_template(example["messages"], tokenize=False)}
    
    def tokenize(sample):
        return tokenizer(sample["text"], padding=True, max_length=128, truncation=True, add_special_tokens=False)
    
    ds = ds.map(preprocess).map(tokenize, remove_columns=ds.column_names)
    
    # 创建recipe
    recipe = [
        SmoothQuantModifier(smoothing_strength=0.8),
        GPTQModifier(targets="Linear", scheme="W8A8", ignore=["lm_head"]),
    ]
    
    print(f"📋 Recipe配置:")
    for i, modifier in enumerate(recipe, 1):
        print(f"   {i}. {type(modifier).__name__}")
    
    # 执行量化
    print(f"\n🔄 执行量化...")
    from llmcompressor.entrypoints.oneshot import oneshot
    
    try:
        quantized_model = oneshot(
            model=model,
            dataset=ds,
            recipe=recipe,
            max_seq_length=128,
            num_calibration_samples=8,
        )
        
        print(f"✅ 量化成功完成!")
        
        # 分析结果
        analyze_simple_flow()
        
        # 分析量化后的模型
        analyze_quantized_model_details(quantized_model)
        
        return quantized_model
        
    except Exception as e:
        print(f"❌ 量化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_simple_flow():
    """分析简化流程"""
    print(f"\n📊 流程分析")
    print("="*80)
    
    global tracker
    
    # 统计事件类型
    event_types = {}
    for event in tracker.events:
        event_type = event['type']
        event_types[event_type] = event_types.get(event_type, 0) + 1
    
    print(f"🔍 事件统计:")
    for event_type, count in event_types.items():
        print(f"   {event_type}: {count}次")
    
    # 分析量化事件
    print(f"\n🔍 量化详情:")
    for event in tracker.events:
        if event['type'] == 'gptq_quantized':
            layer_name = event['layer_name']
            data = event['data']
            
            print(f"\n   📍 {layer_name}:")
            print(f"      权重形状: {data['quantized_weight_shape']}")
            print(f"      scale形状: {data['scale_shape']}")
            print(f"      scale范围: [{data['scale_range'][0]:.8f}, {data['scale_range'][1]:.8f}]")
            print(f"      scale统计: mean={data['scale_mean']:.8f}, std={data['scale_std']:.8f}")
            print(f"      量化损失: {data['quantization_loss']:.8f}")

def analyze_quantized_model_details(model):
    """分析量化后模型的详细信息"""
    print(f"\n📊 量化模型详细分析")
    print("="*80)
    
    linear_layers = []
    for name, module in model.named_modules():
        if 'Linear' in type(module).__name__ and hasattr(module, 'weight_scale'):
            layer_info = {
                'name': name,
                'type': type(module).__name__,
                'weight_shape': list(module.weight.shape),
                'weight_dtype': str(module.weight.dtype),
                'has_weight_scale': hasattr(module, 'weight_scale'),
                'has_weight_zero_point': hasattr(module, 'weight_zero_point'),
                'has_quantization_scheme': hasattr(module, 'quantization_scheme')
            }
            
            if hasattr(module, 'weight_scale'):
                layer_info['weight_scale_shape'] = list(module.weight_scale.shape)
                layer_info['weight_scale_range'] = [
                    module.weight_scale.min().item(),
                    module.weight_scale.max().item()
                ]
                layer_info['weight_scale_mean'] = module.weight_scale.mean().item()
            
            if hasattr(module, 'weight_zero_point'):
                layer_info['weight_zero_point_shape'] = list(module.weight_zero_point.shape)
                layer_info['weight_zero_point_range'] = [
                    module.weight_zero_point.min().item(),
                    module.weight_zero_point.max().item()
                ]
            
            # 检查量化配置
            if hasattr(module, 'quantization_scheme'):
                scheme = module.quantization_scheme
                if hasattr(scheme, 'weights') and scheme.weights:
                    layer_info['weights_config'] = {
                        'bits': scheme.weights.num_bits,
                        'strategy': str(scheme.weights.strategy),
                        'dynamic': scheme.weights.dynamic,
                        'observer': scheme.weights.observer
                    }
                
                if hasattr(scheme, 'input_activations') and scheme.input_activations:
                    layer_info['activation_config'] = {
                        'bits': scheme.input_activations.num_bits,
                        'strategy': str(scheme.input_activations.strategy),
                        'dynamic': scheme.input_activations.dynamic,
                        'observer': scheme.input_activations.observer
                    }
            
            linear_layers.append(layer_info)
    
    print(f"🔍 发现 {len(linear_layers)} 个量化Linear层:")
    
    for layer in linear_layers:
        print(f"\n📍 {layer['name']} ({layer['type']}):")
        print(f"   权重: {layer['weight_shape']} {layer['weight_dtype']}")
        
        if 'weight_scale_shape' in layer:
            print(f"   weight_scale: {layer['weight_scale_shape']}")
            print(f"   scale范围: [{layer['weight_scale_range'][0]:.8f}, {layer['weight_scale_range'][1]:.8f}]")
            print(f"   scale均值: {layer['weight_scale_mean']:.8f}")
        
        if 'weight_zero_point_shape' in layer:
            print(f"   zero_point: {layer['weight_zero_point_shape']}")
            print(f"   zero_point范围: [{layer['weight_zero_point_range'][0]}, {layer['weight_zero_point_range'][1]}]")
        
        if 'weights_config' in layer:
            wc = layer['weights_config']
            print(f"   权重配置: {wc['bits']}位, {wc['strategy']}, dynamic={wc['dynamic']}")
        
        if 'activation_config' in layer:
            ac = layer['activation_config']
            print(f"   激活配置: {ac['bits']}位, {ac['strategy']}, dynamic={ac['dynamic']}")

def main():
    """主函数"""
    print("🔍 SmoothQuant + GPTQ简化流程debug")
    print("="*80)
    
    quantized_model = run_simple_flow_debug()
    
    if quantized_model:
        print(f"\n🎉 Debug完成!")
        print("="*80)
        print("关键发现:")
        print("1. GPTQ使用块级优化但保持per-channel scale格式")
        print("2. Observer计算per-channel scale，GPTQ在此基础上优化")
        print("3. 最终的scale参数完全兼容vLLM的per-channel推理")

if __name__ == "__main__":
    main()
